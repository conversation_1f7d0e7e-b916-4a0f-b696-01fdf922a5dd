<template>
	<div class="chat-dialog" v-if="chatItemSelected">
		<div class="chat-header">
			<div class="title-panel">
				<div class="title">{{ chatItemSelected.title }}</div>
				<div class="sub-title">{{ chatItemSelected.messages.length }} 条对话</div>
			</div>
			<div class="actions">
				<button>
					<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"
						fill="none">
						<defs>
							<path id="rename_svg__a" d="M0 0h16v16H0z"></path>
						</defs>
						<g>
							<mask id="rename_svg__b" fill="#fff">
								<use xlink:href="#rename_svg__a"></use>
							</mask>
							<g mask="url(#rename_svg__b)">
								<path transform="translate(1.775 1.3)" d="M2.83 13.2 13.2 2.83 10.37 0 0 10.37v2.83h2.83Z"
									style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
								</path>
								<path transform="translate(9.317 4.13)" d="m0 0 2.83 2.83"
									style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
								</path>
							</g>
						</g>
					</svg>
				</button>
				<button>
					<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"
						fill="none">
						<defs>
							<path id="share_svg__a" d="M0 0h16v16H0z"></path>
						</defs>
						<g>
							<mask id="share_svg__b" fill="#fff">
								<use xlink:href="#share_svg__a"></use>
							</mask>
							<g mask="url(#share_svg__b)">
								<path d="M6.67 3.67C1.67 3.67 0 7.33 0 13c0 0 2-5 6.67-5v3.67l6-5.67-6-6v3.67Z"
									transform="translate(2 1.333)"
									style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
								</path>
							</g>
						</g>
					</svg>
				</button>
				<button>
					<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"
						fill="none">
						<defs>
							<path id="max_svg__a" d="M0 0h16v16H0z"></path>
						</defs>
						<g>
							<mask id="max_svg__b" fill="#fff">
								<use xlink:href="#max_svg__a"></use>
							</mask>
							<g mask="url(#max_svg__b)">
								<path d="m0 0 3.33 3.3" transform="translate(2 2)"
									style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
								</path>
								<path d="M0 3.3 3.33 0" transform="translate(2 10.667)"
									style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
								</path>
								<path d="M3.3 3.3 0 0" transform="translate(10.7 10.667)"
									style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
								</path>
								<path d="M3.3 0 0 3.3" transform="translate(10.667 2)"
									style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
								</path>
								<path d="M0 0h3v3" transform="translate(11 2)"
									style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
								</path>
								<path d="M3 0v3H0" transform="translate(11 11)"
									style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
								</path>
								<path d="M3 3H0V0" transform="translate(2 11)"
									style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
								</path>
								<path d="M0 3V0h3" transform="translate(2 2)"
									style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
								</path>
							</g>
						</g>
					</svg>
				</button>
			</div>
		</div>
		<div class="chat-body">

			<chat-list ref="chatListDom" :_messages="messages" @inputPrompt="inputPrompt" @reChat="reChat"
				@startChat="playChat"></chat-list>

		</div>
		<div class="chat-input">

			<div class="input-actions">

				<el-tooltip effect="dark" content="设置" placement="top">
					<button>
						<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"
							fill="none">
							<g>
								<mask id="chat-settings_svg__b" fill="#fff">
									<use xlink:href="#chat-settings_svg__a"></use>
								</mask>
								<g mask="url(#chat-settings_svg__b)">
									<path
										d="M6.728 14.598a.665.665 0 0 1-.828.434 7.294 7.294 0 0 1-3.187-1.945.666.666 0 0 1-.026-.896c.207-.241.31-.527.31-.857 0-.37-.13-.685-.389-.944A1.286 1.286 0 0 0 1.663 10h-.08a.662.662 0 0 1-.665-.288.665.665 0 0 1-.098-.235 7.234 7.234 0 0 1 .188-3.675.662.662 0 0 1 .635-.465h.02c.37 0 .684-.13.944-.392.26-.26.39-.574.39-.942 0-.215-.045-.415-.134-.6a.666.666 0 0 1 .148-.78A7.292 7.292 0 0 1 6.034.932a.666.666 0 0 1 .774.34c.11.219.272.394.483.524.216.135.454.202.712.202.254 0 .488-.067.703-.201.211-.131.372-.306.483-.524a.666.666 0 0 1 .773-.34 7.404 7.404 0 0 1 3.03 1.688.664.664 0 0 1 .143.8c-.09.174-.135.368-.135.583 0 .366.13.68.392.941.262.262.575.393.941.393h.03a.664.664 0 0 1 .636.465c.22.695.331 1.43.331 2.201a7.34 7.34 0 0 1-.143 1.474.66.66 0 0 1-.27.411.666.666 0 0 1-.478.115 1 1 0 0 0-.106-.003c-.367 0-.681.13-.941.39-.261.26-.392.574-.392.943 0 .322.106.61.317.865a.662.662 0 0 1 .1.686.671.671 0 0 1-.133.202 7.294 7.294 0 0 1-3.187 1.945.674.674 0 0 1-.503-.047.659.659 0 0 1-.325-.387 1.311 1.311 0 0 0-.477-.667 1.288 1.288 0 0 0-.789-.26c-.297 0-.564.087-.798.26-.23.17-.389.393-.477.667zm3.52-1.032c.626-.253 1.191-.6 1.696-1.042a2.603 2.603 0 0 1-.277-1.19 2.642 2.642 0 0 1 .784-1.888 2.646 2.646 0 0 1 1.51-.753 6.393 6.393 0 0 0-.118-2.067 2.655 2.655 0 0 1-1.394-.738 2.656 2.656 0 0 1-.782-1.884c0-.239.029-.468.087-.687a6.05 6.05 0 0 0-1.693-.954c-.182.22-.398.41-.65.566-.431.268-.9.402-1.408.402-.511 0-.983-.134-1.414-.401a2.632 2.632 0 0 1-.653-.567 5.936 5.936 0 0 0-1.69.957 2.74 2.74 0 0 1-.125 1.72c-.134.319-.324.601-.57.847a2.641 2.641 0 0 1-1.389.738 5.903 5.903 0 0 0-.12 2.069 2.642 2.642 0 0 1 1.509.753 2.654 2.654 0 0 1 .779 1.888c0 .433-.091.831-.274 1.192.504.44 1.068.787 1.692 1.04.172-.272.393-.507.664-.707.47-.348 1-.522 1.591-.522.587 0 1.115.174 1.584.523.27.2.49.435.661.705z"
										style="fill: rgb(51, 51, 51); opacity: 1;"></path>
									<path
										d="M10.122 10.122a2.987 2.987 0 0 1-2.125.881 2.969 2.969 0 0 1-2.122-.883 2.983 2.983 0 0 1-.64-.955A2.984 2.984 0 0 1 5 7.997a2.974 2.974 0 0 1 .876-2.12A2.974 2.974 0 0 1 7.996 5c.407 0 .797.078 1.169.235.36.151.678.364.955.64a2.969 2.969 0 0 1 .883 2.122 2.987 2.987 0 0 1-.881 2.125zM7.997 6.333c-.461 0-.854.162-1.178.486a1.603 1.603 0 0 0-.486 1.178c0 .462.163.857.488 1.183.324.327.716.49 *********** 0 .855-.164 1.182-.491.327-.327.491-.721.491-1.182 0-.46-.163-.852-.49-1.176a1.618 1.618 0 0 0-1.183-.488z"
										style="fill: rgb(51, 51, 51); opacity: 1;"></path>
								</g>
							</g>
							<defs>
								<path id="chat-settings_svg__a" d="M0 0h16v16H0z"></path>
							</defs>
						</svg>
					</button>
				</el-tooltip>

				<el-tooltip effect="dark" content="快捷方式" placement="top">
					<button>
						<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"
							fill="none">
							<defs>
								<path id="prompt_svg__a" d="M0 0h16v16H0z"></path>
							</defs>
							<g>
								<mask id="prompt_svg__b" fill="#fff">
									<use xlink:href="#prompt_svg__a"></use>
								</mask>
								<g mask="url(#prompt_svg__b)">
									<path
										d="m1.367 1.367 1.41 1.41M4.667 0v2m0 0V0m3.295 1.367-1.41 1.41m0 0 1.41-1.41m1.371 3.3h-2m0 0h2m-1.37 3.295-1.41-1.41m0 0 1.41 1.41M4.666 9.333v-2m0 0v2m-3.3-1.37 1.41-1.41m0 0-1.41 1.41M0 4.666h2m0 0H0"
										transform="translate(5.333 1.333)"
										style="stroke: rgb(51, 51, 51); stroke-width: 1.3; stroke-opacity: 1; stroke-dasharray: 0, 0;">
									</path>
									<path d="M8.01 0 0 8.01" transform="translate(1.848 6.138)"
										style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
									</path>
								</g>
							</g>
						</svg>
					</button>
				</el-tooltip>

				<el-tooltip effect="dark" content="面具" placement="top">
					<button>
						<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"
							fill="none">
							<defs>
								<path id="mask_svg__a" d="M0 0h16v16H0z"></path>
							</defs>
							<g>
								<mask id="mask_svg__b" fill="#fff">
									<use xlink:href="#mask_svg__a"></use>
								</mask>
								<g mask="url(#mask_svg__b)">
									<path d="M6 0C2.69 0 0 2.54 0 5.67s2.69 5.66 6 5.66 6-2.53 6-5.66C12 2.54 9.31 0 6 0Z"
										transform="translate(2 3.333)"
										style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
									</path>
									<path d="M1 0C.45 0 0 .6 0 1.33c0 .74.45 1.34 1 1.34s1-.6 1-1.34C2 .6 1.55 0 1 0Z"
										transform="rotate(15 -22.183 22.313)"
										style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
									</path>
									<path d="M1 0C.45 0 0 .6 0 1.33c0 .74.45 1.34 1 1.34s1-.6 1-1.34C2 .6 1.55 0 1 0Z"
										transform="rotate(165 5.213 5.304)"
										style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
									</path>
									<path d="M4 4.51c1.04-1.04 1.15-2.74.1-3.78C3.06-.32 1.04-.2 0 .84" transform="translate(9.667 2.493)"
										style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
									</path>
									<path d="M.84 4.51C-.2 3.47-.32 ********** 1.77-.32 3.8-.2 4.84.84" transform="translate(1.493 2.493)"
										style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
									</path>
									<path d="M0 0c.17.43.73 1.09 **********.8 1.5.14 1.66-.29" transform="translate(6.5 11.67)"
										style="stroke: rgb(51, 51, 51); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
									</path>
								</g>
							</g>
						</svg>
					</button>
				</el-tooltip>

				<el-tooltip effect="dark" content="清除聊天" placement="top">
					<button @click="clearMessages">
						<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"
							fill="none">
							<path
								d="M13.275-.275c.261.26.392.576.392.945v10.66c0 .37-.131.684-.392.945a1.288 1.288 0 0 1-.945.392H1.67c-.37 0-.684-.13-.945-.392a1.288 1.288 0 0 1-.392-.945V.67c0-.37.13-.684.392-.945.26-.261.576-.392.945-.392h10.66c.369 0 .684.13.945.392zM1.667 11.33c0 .002 0 .003.003.003h10.66c.002 0 .003 0 .003-.003V.67c0-.002 0-.003-.003-.003H1.67c-.002 0-.003 0-.003.003z"
								transform="translate(1 2)" style="fill: rgb(51, 51, 51); opacity: 1;"></path>
							<path
								d="M9.763 7.507a.666.666 0 0 1-.867 0L7 5.878 5.104 7.506a.665.665 0 0 1-.867.001L2.331 5.878.434 7.506a.66.66 0 0 1-.485.16.66.66 0 0 1-.53-.338.665.665 0 0 1 .147-.834l2.33-2a.666.666 0 0 1 .867 0L4.67 6.123l1.897-1.629a.666.666 0 0 1 .868 0l1.897 1.629 1.906-1.63a.668.668 0 0 1 .867.002l2.33 2a.66.66 0 0 1 .23.455.661.661 0 0 1-.252.575.666.666 0 0 1-.846-.019l-1.897-1.628z"
								transform="translate(1 2)" style="fill: rgb(51, 51, 51); opacity: 1;"></path>
							<g>
								<mask id="break_svg__b" fill="#fff">
									<use xlink:href="#break_svg__a"></use>
								</mask>
							</g>
							<defs>
								<path id="break_svg__a" d="M0 0h16v16H0z"></path>
							</defs>
						</svg>
					</button>
				</el-tooltip>

				<button>
					<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"
						fill="none">
						<g>
							<mask id="robot_svg__b" fill="#fff">
								<use xlink:href="#robot_svg__a"></use>
							</mask>
							<g mask="url(#robot_svg__b)">
								<path
									d="M3.67 13.67h8.66c.002 0 .003-.001.003-.003V6.334H3.67c-.002 0-.003 0-.003.003v7.33c0 .002 0 .003.003.003zm0 1.333c-.37 0-.684-.13-.945-.391a1.288 1.288 0 0 1-.392-.945v-7.33c0-.37.13-.685.392-.946.26-.26.576-.391.945-.391h8.66c.37 0 .684.13.945.391.261.261.392.577.392.946v7.33c0 .369-.13.684-.392.945a1.288 1.288 0 0 1-.945.391z"
									style="fill: rgb(51, 51, 51); opacity: 1;"></path>
								<path
									d="M9.819 6.127a.666.666 0 0 1-1.05-.81l1.67-2.67a.665.665 0 0 1 1.107-.034.663.663 0 0 1 .023.74l-1.67 2.67a.665.665 0 0 1-.08.104zM7.293 5.434a.665.665 0 0 1-1.01.779.665.665 0 0 1-.178-.19l-1.67-2.67a.665.665 0 0 1 .952-.896.665.665 0 0 1 .178.19l1.67 2.67a.665.665 0 0 1 .058.117z"
									style="fill: rgb(51, 51, 51); opacity: 1;"></path>
								<path
									d="M11.337 2.333c-.003 0-.004.001-.004.004l-.001-.01a.007.007 0 0 0 .005.003h-.007v.007c0-.002 0-.004-.002-.005l.009.001zm0-1.333c.366 0 .679.131.939.394.258.261.387.575.387.943 0 .364-.13.676-.39.936s-.572.39-.936.39c-.368 0-.682-.129-.943-.388A1.273 1.273 0 0 1 10 2.337c0-.37.13-.685.391-.946.262-.26.577-.391.946-.391zM4.67 2.333c-.002 0-.003.001-.003.004 0-.005 0-.008-.002-.01a.007.007 0 0 0 .005.003h-.006v.007c0-.002-.001-.004-.003-.005l.009.001zM4.67 1c.366 0 .679.131.939.394.258.261.388.575.388.943 0 .364-.13.676-.39.936s-.573.39-.937.39c-.367 0-.681-.129-.943-.388a1.273 1.273 0 0 1-.394-.938c0-.37.13-.685.392-.946.26-.26.576-.391.945-.391zM6.663 10.003h2.67c.184 0 .34-.065.469-.194a.655.655 0 0 0 .195-.479.638.638 0 0 0-.196-.467.638.638 0 0 0-.468-.196h-2.67a.638.638 0 0 0-.467.196A.638.638 0 0 0 6 9.33c0 .188.065.347.195.479.128.13.284.194.468.194zm0 1.334a1.974 1.974 0 0 1-1.416-.59 2.001 2.001 0 0 1 1.416-3.413h2.67a1.99 1.99 0 0 1 1.41.587 1.99 1.99 0 0 1 .587 1.409 2.002 2.002 0 0 1-1.997 2.007zM2 10.663h1.003a.666.666 0 1 1 0 1.334h-1.67a.666.666 0 0 1-.666-.667V8a.665.665 0 0 1 .666-.667h1.67a.665.665 0 0 1 .555 1.037.665.665 0 0 1-.555.297H2zM14.8 7.346a.664.664 0 0 1 .537.654v3.33a.666.666 0 0 1-.667.667H13a.666.666 0 1 1 0-1.334h1.003V8.667H13a.665.665 0 0 1-.471-1.138.664.664 0 0 1 .471-.196h1.67c.044 0 .087.005.13.013z"
									style="fill: rgb(51, 51, 51); opacity: 1;"></path>
							</g>
						</g>
						<defs>
							<path id="robot_svg__a" d="M0 0h16v16H0z"></path>
						</defs>
					</svg>
					<span class="ml10 robot">ChatGPT 3.5</span>
				</button>

			</div>

			<div class="input-panel">
				<div class="input">
					<codejar-input class="input-prompt" :_content="prompt" @input="inputPrompt"></codejar-input>
				</div>
				<button class="sendchat" @click="sendchat">
					<svg class="mr6" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="16"
						height="16" fill="none">
						<defs>
							<path id="send-white_svg__a" d="M0 0h16v16H0z"></path>
						</defs>
						<g>
							<mask id="send-white_svg__b" fill="#fff">
								<use xlink:href="#send-white_svg__a"></use>
							</mask>
							<g mask="url(#send-white_svg__b)">
								<path transform="translate(1.333 2)" d="M0 4.71 6.67 6l1.67 6.67L12.67 0 0 4.71Z"
									style="stroke: rgb(255, 255, 255); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
								</path>
								<path transform="translate(8.003 6.117)" d="M0 1.89 1.89 0"
									style="stroke: rgb(255, 255, 255); stroke-width: 1.33333; stroke-opacity: 1; stroke-dasharray: 0, 0;">
								</path>
							</g>
						</g>
					</svg>
					发送
				</button>
			</div>
		</div>
	</div>
</template>

<script>

import CodejarInput from "@/components/codejar-input.vue";
import ChatList from "@/views/layout-left/left/chat-list.vue";
import { Message } from "@/defines/Message";
import { startTalk } from "@/libs/talk";
import { findId, findLast } from "@/helps";

import $ from "cash-dom";
import { genKey } from "@/utils/string";

export default {
	name: "chat-dialog",
	components: { ChatList, CodejarInput },

	async mounted() {
		// 绑定快捷键
		this.bindSKeys()
	},

	data() {

		return {
			pinned: false,
			editorHeight: '300',
			prompt: '', //
			isTalking: false,
			// messages: [{
			// role: "system",
			// content: "你是 OpenAI 训练的大型语言模型。",
			// }]
		}
	},

	computed: {
		chatItemSelected() {
			return this.$store.getters.chatItemSelected;
		},

		messages() {
			return this.chatItemSelected?.messages;
		}
	},

	methods: {

		// 添加 message
		addChatMessage(msg) {
			this.messages.push(msg)
		},

		clearMessages() {
			this.chatItemSelected.messages = this.messages.filter(m => m.role == 'system')
		},

		bindSKeys() {
			let _this = this
			$('.prompt-panel').find('.jar-editor').on('keydown', function (event) {
				if (event.key == 'Enter' && event.metaKey) {
					// 发送
					_this.sendchat()
				} else if (event.key == 'Enter' && event.shiftKey) {
					event.preventDefault()
					_this.addPrompt()
				}
			})
		},

		addPrompt() {
			if (this.prompt) {
				this.messages.push(Message({ role: "user", content: this.prompt }))
				this.prompt = ''
			}
		},

		setEditorHeight() {
			let offheight = document.getElementsByClassName("editor-textarea")
			this.editorHeight = (offheight[0].offsetHeight - 25).toString()
		},

		async switchpin() {
			this.pinned = !this.pinned
			await ipcRenderer.invoke('app/pinEditor', this.pinned)
		},

		async playChat(id) {

			let idx = this.messages.findIndex(e => e.id == id) + 1
			let nextId = genKey()

			if (idx >= this.messages.length - 1) {
				this.messages.push(Message({ id: nextId, role: "assistant", content: '' }))
			} else {
				this.messages.splice(idx, 0, { id: nextId, role: "assistant", content: '' }) // content 为 0 的时候就在启动中...
			}

			await this.reChat(nextId)
		},

		async reChat(id) {

			let idx = this.messages.findIndex(t => t.id == id)
			this.$set(this.messages[idx], 'content', '')
			this.$set(this.messages[idx], 'loading', true)

			let messages = this.messages.slice(0, idx + 1)

			await startTalk({
				messages,
				end: () => {
					this.$set(this.messages[idx], 'loading', false)
					this.enableCopyAction()
				},
				err: (e) => {
					if (e.toString().startsWith('AbortError')) {
						this.appendMessageContent(' ...', id)
					} else {
						this.appendMessageContent('\n[ ' + e.message + ' ]', id)
					}
				},
				read: (content) => {
					this.appendMessageContent(content, id)
				}
			})

		},

		async sendchat() {

			this.addPrompt()

			this.messages.push(Message({ role: "assistant", content: '' }))

			this.$refs.chatListDom.scrollToBottom()
			this.$refs.chatListDom.start()

			// console.log('-----------------')
			// console.log(this.messages)
			// console.log('-----------------')

			// 开始 talk
			await startTalk({
				messages: this.messages,
				end: () => {
					this.$refs.chatListDom.end()
					this.enableCopyAction()
				},
				err: (e) => {
					if (e.toString().startsWith('AbortError')) {
						this.appendMessageContent(' ...')
					} else {
						this.appendMessageContent('\n[' + e.message + ']')
					}
				},
				read: (content) => {
					this.appendMessageContent(content)
				}
			})
		},

		inputPrompt(val) {
			this.prompt = val
		},

		appendMessageContent(content, id) {
			if (id) {
				let msg = findId(this.messages, id)
				msg.content += content
			} else {
				let msg = findLast(this.messages)
				msg.content += content
				this.$refs.chatListDom.scrollToBottom()
			}
		},

		enableCopyAction() {
			$('.copy-action').on('click', function () {

				let wrapper = $(this).closest('.code-block-wrapper')
				let code = wrapper.find('code').text()
				wrapper.find('.copy-action').addClass('copied')

				setTimeout(() => {
					wrapper.find('.copy-action').removeClass('copied')
				}, 6000)

				ipcRequest('help/copy', code)
			})
		}
	}
}
</script>

<style lang="scss">
.chat-dialog {

	display: flex;
	flex-direction: column;
	position: relative;
	height: 100%;

	.chat-header {

		padding: 14px 20px;
		border-bottom: 1px solid rgba(0, 0, 0, .1);
		position: relative;
		display: flex;
		justify-content: space-between;
		align-items: center;

		&>.title-panel {

			max-width: calc(100% - 100px);
			overflow: hidden;

			.title {
				font-size: 20px;
				font-weight: bolder;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				display: block;
				max-width: 50vw;
			}

			.sub-title {
				font-size: 14px;
			}
		}


		&>.actions {

			display: inline-flex;

			button {
				border: var(--border-in-light);
				background-color: var(--white);
				border-radius: 10px;
				display: flex;
				align-items: center;
				margin-right: 10px;
				justify-content: center;
				padding: 10px;
				cursor: pointer;
				transition: all .3s ease;
				overflow: hidden;
				-webkit-user-select: none;
				-moz-user-select: none;
				user-select: none;
				outline: none;
				color: var(--black);
			}

		}
	}

	.chat-body {
		flex: 1 1;
		overflow: auto;
		overflow-x: hidden;
		padding: 10px 20px 10px;
		position: relative;
		overscroll-behavior: none;
	}

	.chat-input {
		padding-bottom: 10px;
	}

	.input-actions {
		padding: 10px;
		border-top: var(--border-in-light);

		button {
			display: inline-flex;
			border-radius: 20px;
			font-size: 12px;
			margin-right: 6px;
			background-color: var(--white);
			color: var(--black);
			border: var(--border-in-light);
			padding: 4px 10px;
			animation: chat_slide-in__nvZgA .3s ease;
			box-shadow: var(--card-shadow);
			transition: width .3s ease;
			align-items: center;
			height: 26px;
			width: var(--icon-width);
			overflow: hidden;
		}
	}

	span.robot {
		font-weight: bold;
		font-size: 12px;
	}

	.input-panel {
		//position: relative;
		//width: 100%;
		//padding: 10px 20px 20px;

		padding: 0 10px 0 10px;

		//box-shadow: var(--card-shadow);

		//width: 100%;
		display: flex;

		.input {
			flex-grow: 1;
		}

		.codejar-input {
			border-radius: 10px;
			border: var(--border-in-light);
			min-height: 68px;
		}

		button {
			height: 30px;
			margin-left: 10px;
		}

		.sendchat {
			background-color: var(--primary);
			color: #fff;
			line-height: 40px;
			height: 40px;
			border-radius: 10px;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 10px;
			cursor: pointer;
			transition: all .3s ease;
			overflow: hidden;
			-webkit-user-select: none;
			-moz-user-select: none;
			user-select: none;
			outline: none;
			border: none;
		}

	}


}
</style>
