import { chatgpt } from "@/apis/chat";

const textDecoder = new TextDecoder("utf-8");

export async function startTalk({msgId, end, err, read, start}) {

    try {
        const {body, status} = await chatgpt(msgId)
        if (body) {
            const reader = body.getReader()
            await readStream(reader, status, read)
            start()
        }
    } catch (e) {
        err(e)
    } finally {
        end()
    }
}

export async function readStream(reader, status, callback) {

    let partialLine = "";

    while (true) {

        const {value, done} = await reader.read();

        if (done) break;

        const decodedText = textDecoder.decode(value, {stream: true});

        // console.log(decodedText)

        if (status !== 200) {

            const content = json.error.message ?? decodedText
            callback(content)

        } else {

            const chunk = partialLine + decodedText;

            const newLines = chunk.split(/\r?\n/);

            partialLine = newLines.pop() ?? "";

            for (const line of newLines) {

                if (line.length === 0) continue; // ignore empty message
                if (line.startsWith("id:")) continue; // ignore sse comment message
                if (line === "data: [DONE]") return;

                if (line.startsWith('data: ')) {

                    let content = line.substring(6).replace(/^\n+/, 'XXXX');


                    // if (content == '\\n') {
                    //     content.log('xxx')
                    //     debugger
                    //     content = '\n'
                    // }

                    callback(content)
                }
            }
        }
    }
}
