//@import './custom-elementui.scss';

//  ================================================================================================
//  ? TIP:  It is recommended to use this file for overriding bootstrap variables.
//  ================================================================================================

$blackColor_L1: #b9b9c3;
$blueColor_L1: #7367f0;

// ----------

$bordColor: #DBDBDB;
$lightBordColor: #ebe9f1;

$primaryColor: #7367f0 !important;
//$primaryBgColor: #EEEDFC;
//$primaryBgColor: #EFEFF4;
$primaryBgColor: #f3f2f7;


$border: 1px solid $bordColor;
$borderRadius: 4px;

$greyColor: #b9b9c3 !important;

//$blueColor:#5d6494;
//$greenColor:#66c23a;
//$redColor:#f56c6c;
//$primaryColor_bg

$redColor: #db2828;
$orangeColor: #f2711c;
$yellowColor: #fbbd08;
$greenColor: #21ba45;
$blueColor: #5d6494;
$violetColor: #6435c9;
$pinkColor: #e03997;

$blackLv1: #303133;
$blackLv2: #606266;
$blackLv3: #909399;
$blackLv4: #C0C4CC;


$fontFamily_main: "Montserrat", "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
$fontFamily_editor: "CascadiaMono", "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;


// -------------------------

.flex-between {
  display: flex;
  justify-content: space-between;
}

.inline-flex {
  display: inline-flex;
}

.over-hidden {
  overflow: hidden
}

.minwidth-20 {
  min-width: 20px;
}

.height100vh {
  height: 100vh;
}

.height100 {
  height: 100%;
}

.h-100vh {
  height: 100vh;
}

.h-100 {
  height: 100%;
}


.w-100, .width100 {
  width: 100%;
}

.pointer {
  cursor: pointer;
}

.right {
  float: right;
}

.left {
  float: left;
}

.nopadding {
  padding: 0 !important;
  margin: 0 !important;
}

.nopadding-right {
  padding-right: 0 !important;
  margin-right: 0 !important;
}

.nopadding-children > * {
  padding: 0;
  margin: 0;
}

.center {
  text-align: center;
}

.rotate45 {
  transform: rotate(45deg);
}

.rotate90 {
  transform: rotate(90deg);
}

.text-align-left {
  text-align: left;
}

.modal-dialog {
  .footer {
    margin-bottom: 10px;
  }
}

.table thead th, .table tfoot th {
  text-transform: inherit;
}

.no-btn {
  &:hover,
  &:focus,
  &:active {
    background-color: rgba(130, 134, 139, 0) !important;
    color: #7468f0 !important;
  }
}

button.btn-sm {
  padding: 4px 10px;
  font-size: 11px;
}

input.form-control-sm {
  font-weight: 500 !important;
  height: 25px !important;
}

.clearfix {
  clear: both;
}

.flex {
  display: flex;
  &.space-between {
    justify-content: space-between
  }
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

.flex-end {
  display: flex;
  justify-content: flex-end;
}


.flex-wrap {
  flex-wrap: wrap;
}

.font-bolder {
  font-weight: bolder;
}

.font-normal {
  font-weight: normal;
}

.relative {
  position: relative;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.spin {
  display: inline-block;
  animation: spin 2s linear infinite;
}


/*
[md10] => / margin-bottom: 10px !important; /
 */

@for $size from 6 through 60 {
  .font-size_#{$size} {
    font-size: #{$size}px;
  }
}

@for $size from 10 through 100 {
  .line-height_#{$size} {
    line-height: #{$size}px;
  }
}

$margin-directions: ("mr": "margin-right", "mb": "margin-bottom", "ml": "margin-left", "mt": "margin-top");
@each $key, $value in $margin-directions {
  @for $i from 2 through 20 {
    .#{$key}#{$i} {
      #{$value}: #{$i}px
    }
  }
}

@each $key, $value in $margin-directions {
  @for $i from 1 through 6 {
    .#{$key}#{$i*10} {
      #{$value}: #{$i*10}px
    }
  }
}

$padding-directions: ("padding":"padding", "pr": "padding-right", "pb": "padding-bottom", "pl": "padding-left", "pt": "padding-top");
@each $key, $value in $padding-directions {
  @for $i from 2 through 20 {
    .#{$key}#{$i} {
      #{$value}: #{$i}px
    }
  }
}

@each $key, $value in $padding-directions {
  @for $i from 1 through 6 {
    .#{$key}#{$i*10} {
      #{$value}: #{$i*10}px
    }
  }
}
