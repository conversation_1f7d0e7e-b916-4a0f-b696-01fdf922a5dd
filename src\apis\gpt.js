let abortCtr; // 声明一个控制器变量

export async function chatgpt(messageList, apiKey) {

    let newMessageList = messageList.map(({role, content}) => ({role, content}));
    // let messages = newMessageList.filter(item => item.role == 'system' || item.role == 'user')

    let messages = newMessageList

    try {

        abortCtr = new AbortController()

        const result = await fetch("https://api.openai.com/v1/chat/completions", {
            method: "post",
            signal: abortCtr.signal, // 传递中断信号
            // signal: AbortSignal.timeout(8000),
            // 开启后到达设定时间会中断流式输出
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${apiKey}`,
            },

            body: JSON.stringify({

                model: "gpt-3.5-turbo",
                // model: "code-davinci-002",
                // model: "gpt-4",
                // model: "code-davinci-002",
                // model: "code-davinci-edit-001",

                temperature: 0.1,
                max_tokens: 3000,
                top_p: 0.1,
                stream: true,
                messages,
            }),
        });

        return result
    } catch (error) {
        throw error
    }
}

// 取消chatgpt
export function cancelChatgpt() {
    if (abortCtr) {
        abortCtr.abort(); // 中断请求
    }
}
