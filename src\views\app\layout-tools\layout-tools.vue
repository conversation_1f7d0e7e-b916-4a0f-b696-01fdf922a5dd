<template>
	<el-main class="layout-draw">
		<el-container>
			<layout-tools-left @changeActiveClassId="createChat" @changeAgentId="createAgent"></layout-tools-left>
			<layout-tools-main :toolId="toolId" v-if="!isAgent"></layout-tools-main>
			<layout-tools-agent :agentId="agentId" :name="agentName" v-else></layout-tools-agent>
		</el-container>
	</el-main>
</template>

<script>
import LayoutToolsLeft from '@/views/app/layout-tools/layout-tools-left.vue';
import LayoutToolsMain from '@/views/app/layout-tools/layout-tools-main.vue';
import LayoutToolsAgent from '@/views/app/layout-tools/layout-tools-agent.vue';

export default {
	name: 'layout-tools',
	components: { LayoutToolsLeft, LayoutToolsMain, LayoutToolsAgent },

	async mounted() {},

	data() {
		return {
			genImages: [],
			status: '0', // 0 - 未处理，1 - 处理中，2 - 已经完成
			config: {},
			toolId: 0,
			agentId: 0,
			agentName: '',
			isAgent: false,
		};
	},

	methods: {
		createChat(id) {
			this.isAgent = false;
			this.$nextTick(() => {
				this.toolId = id;
			});
		},
		createAgent(data) {
			this.toolId = 0;
			this.isAgent = true;
			this.agentId = data.guid;
			this.agentName = data.agentName;
		},
	},
};
</script>

<style lang="scss">
.layout-draw {
	//border-right: solid 1px var(--border-color);
	padding: 0 !important;
}
</style>
