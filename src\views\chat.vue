<template>
    <el-container class="chat">
        <layout-left></layout-left>
        <layout-main></layout-main>
    </el-container>
</template>

<script>
import LayoutLeft from "@/views/layout-left.vue";
import LayoutMain from "@/views/layout-main.vue";

export default {
    name: "chat",
    components: {LayoutLeft, LayoutMain},
}
</script>

<style lang="scss">
.chat {
    border: var(--border-in-light);
    border-radius: 20px;
    box-shadow: var(--shadow);
    color: var(--black);
    background-color: var(--white);
    min-width: 600px;
    min-height: 370px;
    max-width: 1200px;
    display: flex;
    overflow: hidden;
    box-sizing: border-box;
    //width: var(--window-width);

    width: 80vw;
    height: var(--window-height);
}
</style>
