/**
 * randomStr 生成随机字符串
 */
export const randomStr = function (num, maxA, minlA, fqy) {
    // num:随机数中是否包含数字
    // maxA:随机数中是否包大写字母
    // minlA:随机数中是否包小写字母
    // fqy:生成多少位随机数
    let arr = []
    let arr1 = []
    let arr2 = []
    if (num) {
        for (let m = 0; m <= 9; m++) {
            arr.push(m)
        }
    }
    if (maxA) {
        for (let m = 65; m <= 90; m++) {
            arr1.push(m)
        }
    }
    if (minlA) {
        for (let m = 97; m <= 122; m++) {
            arr2.push(m)
        }
    }
    if (!fqy) {
        console.log('生成位数必传')
        return
    }
    let mergeArr = arr.concat(arr1);
    let mergeArr1 = mergeArr.concat(arr2);
    let _length = mergeArr1.length
    let text = ''
    for (let m = 0; m < fqy; m++) {
        let text1 = ''
        let random = randomNum(0, _length)
        if ((mergeArr1[random]) <= 9) {
            text1 = mergeArr1[random]
        } else if ((mergeArr1[random]) > 9) {
            text1 = String.fromCharCode(mergeArr1[random])
        }
        text += text1
    }
    return text
};

/**
 * randomNum 生成随机数
 * @param {number} max 最大
 * @param {number} mix 最小
 */
export const randomNum = function (a, b) {
    var max = a;
    var min = b;
    if (a < b) {
        max = b;
        min = a;
    }
    return parseInt(Math.random() * (max - min)) + min;
}

var Hashes = require('jshashes')
var MD5 = new Hashes.MD5;
var Hash = new Hashes.SHA1;

// import { getSecretKey } from "@/store/user/userConfig.js"
/**
 * autographFun 生成签名函数
 */
export const autographFun = (configData) => {
    let config = configData;
    let signData;
    let queryData = {
        app_guid: config.urlSuffix.app_guid,
        app_type: config.urlSuffix.app_type,
        token: config.urlSuffix.token
    }
    let queryMd5 = MD5.hex(JSON.stringify(queryData))
    if (config.method === 'POST') {
        let dataMd5 = config.data ? MD5.hex(JSON.stringify(config.data)) : ''
        signData = config.method + '\n' + dataMd5 + '\n' + queryMd5 + '\n' + 'application/json' + '\n' + config
            .urlSuffix.expires + '\n' + config.urlSuffix.noncestr + '\n' + '/' + (config.url.toLowerCase())
    } else {
        signData = config.method + '\n' + queryMd5 + '\n' + config.urlSuffix.expires + '\n' + config.urlSuffix
            .noncestr + '\n' + '/' + (config.url.toLowerCase())
    }

    //签名key
    // let secretKey = store.state.secretKey || store.state.touristSecretKey || ''
    // if (!secretKey) {
    //     console.log('未进行任何登录')
    //     secretKeyFun()
    // }
    // let secretKey = getSecretKey() || ''
    // return Hash.b64_hmac(secretKey, signData)

    return Hash.b64_hmac(config.urlSuffix.token, signData)
}
