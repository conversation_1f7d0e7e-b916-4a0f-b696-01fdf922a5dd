const {defineConfig} = require('@vue/cli-service')
const path = require('path')

module.exports = defineConfig({

    lintOnSave: false,

    transpileDependencies: true,

    configureWebpack: {
        resolve: {
            alias: {
                '@': path.resolve(__dirname, 'src'),
            },
        },
    },

    devServer: {
        // 配置 start
        client: {
            overlay: false
        },
        // 配置 end
    }

})
