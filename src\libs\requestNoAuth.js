import axios from 'axios'
import { Message } from 'element-ui'
// import router from '@/router/index'
import store from '@/store'
import { getToken, removeSession } from '@/utils/auth'
import { parseInt } from 'lodash'
import { randomStr, queryStr } from '@/libs/request-random'
import { autographFun } from '@/libs/request-generateSignature'

const service = axios.create({
  baseURL: 'https://ai-api.deepcity.cn/',
  timeout: 5000, // request timeout
  headers: { 'Content-Type': 'application/json' },
  transformRequest: [function (data) {
    return JSON.stringify(data) || {}
  }]
})

// service.interceptors.request.use(config => {
//         return config
//     },
//     error => {
//         console.log(error)
//         return Promise.reject(error)
//     }
// )

service.interceptors.response.use(
  response => {
    const res = response
    return res.data
  },
  error => {
    Message({
      message: error.msg ? error.msg : '请求失败，请刷新重试',
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
