<template>
    <div class="chat-list" ref="chatListDom">

        <!--        <div class="center tech-info">-->
        <!--            <div class="mb10 mt10"><i class="iconfont icon-service_ChatGPT"></i></div>-->
        <!--            <div>AI 能力由 chatgpt 3.5 模型提供</div>-->
        <!--        </div>-->

        <div class="message" v-wave v-if="!isSystem(msg)" :class="msgClass(msg)" :key="msg.id"
             v-for="(msg,idx) of _messages">

            <div class="toolbar-loading" v-if="isSystemOrAssistant(msg) && msg.loading">
                <a class="rotate">
                    <feather-icon class="rotate" icon="LoaderIcon" size="16"/>
                </a>
                <a class="pause" @click="cancelChat">
                    <feather-icon class="ml10" icon="PauseCircleIcon" size="16"/>
                </a>
            </div>

            <div class="toolbar" v-if="!isSystem(msg)">

                <!-- 发送 -->
                <el-tooltip class="item" effect="dark" content="发送" placement="top">
                    <span class="ml4" v-if="isUser(msg)" variant="light-primary" @click="starChat(msg)">
                        <feather-icon icon="PlayIcon" size="11"/>
                    </span>
                </el-tooltip>

                <!-- 重新生成 -->
                <el-tooltip class="item" effect="dark" content="重新生成" placement="top">
                    <span class="ml4" v-if="isAssistant(msg)" variant="light-primary" @click="reChat(msg)">
                        <feather-icon icon="RefreshCwIcon" size="11"/>
                    </span>
                </el-tooltip>

                <!-- 编辑 -->
                <span class="ml4" variant="light-primary" @click="edit(msg.id)">
                    <feather-icon icon="Edit3Icon" size="11"/>
                </span>

                <!-- 复制 -->
                <el-tooltip effect="dark" content="复制" placement="top">
                    <span class="ml4" v-show="!isStarting(msg) || !msg.loading" v-wave-trigger variant="light-primary"
                          @click="copy(msg)">
                        <feather-icon icon="CopyIcon" size="11"/>
                    </span>
                </el-tooltip>

                <!-- 引用 -->
                <el-tooltip effect="dark" content="引用" placement="top">
                    <span class="ml4" v-show="!isStarting(msg) || !msg.loading" v-wave-trigger variant="light-primary"
                          @click="quote(msg.content)">
                        <feather-icon icon="HashIcon" size="11"/>
                    </span>
                </el-tooltip>

                <!-- 删除 -->
                <el-tooltip effect="dark" content="删除" placement="top">
                    <span class="ml4" v-show="!isStarting(msg) || !msg.loading" v-wave-trigger variant="light-primary"
                          @click="remove(msg.id)">
                        <feather-icon icon="TrashIcon" size="11"/>
                    </span>
                </el-tooltip>

                <!-- 更多 -->
                <!--                <el-dropdown trigger="click" size="small" :show-timeout="100" :hide-timeout="100"-->
                <!--                             placement="bottom-start">-->
                <!--                    <span class="ml4 el-dropdown-link pointer" variant="light-primary">-->
                <!--                        <feather-icon icon="MoreHorizontalIcon" size="11"/>-->
                <!--                    </span>-->
                <!--                    <el-dropdown-menu v-slot="dropdown" placement="top" class="i-dropdown-menu chat-opts-dropdown-menu">-->
                <!--                        <el-dropdown-item class="dropitem" @click.native="quote(msg.content)">-->
                <!--                            <feather-icon class="mr10 mb3" stroke-width="3" size="10" icon="AnchorIcon"></feather-icon>-->
                <!--                            引用-->
                <!--                        </el-dropdown-item>-->
                <!--                        <el-dropdown-item class="dropitem" @click.native="remove(msg.id)">-->
                <!--                            <feather-icon class="mr10 mb3" stroke-width="3" size="10" icon="Trash2Icon"></feather-icon>-->
                <!--                            删除-->
                <!--                        </el-dropdown-item>-->
                <!--                    </el-dropdown-menu>-->
                <!--                </el-dropdown>-->

            </div>

            <div v-if="isUser(msg)">
                <codejar-input :id="msg.id" class="user-prompt" @input="(val)=>{inputContent(msg.id,val)}"
                               :_content="msg.content"></codejar-input>
            </div>

            <div class="md-htm" v-if="isSystemOrAssistant(msg)" v-html="renderHtml(msg.content)"></div>

            <!-- 刷新 -->
            <div class="starting center" v-show="isStarting(msg)">
                <a>
                    <feather-icon class="rotate" icon="LoaderIcon" size="14"/>
                </a>
            </div>

        </div>

    </div>
</template>

<script>

import { md } from "@/libs/markdown";
import { cancelChatgpt } from "@/apis/gpt";
import CodejarInput from "@/components/codejar-input.vue";
import Badge from "@/components/badge.vue";

export default {

    name: 'chat-list',
    components: {Badge, CodejarInput},

    props: {
        _messages: Array
    },

    data() {
        return {}
    },

    methods: {

        // 收藏...
        // async mark(msg) {
        //     let data = {id: msg.id, gist: msg.content}
        //     await ipcRequest('gist/saveFavor', data)
        //     let idx = this._messages.findIndex(e => e.id == msg.id)
        //     this.$set(this._messages[idx], 'is_marked', true)
        // },

        inputContent(id, val) {
            // let idx = this._messages.findIndex(item => item.id == id)
            // this.$set(this._messages[idx], 'content', val)
        },

        async toggleFavor(msg) {
            if (!msg.is_marked) {
                // let postdata = {id: msg.id, gist: msg.content}
                // let {status} = await ipcRequest('gist/saveFavor', postdata)
                // if (status == 1) {
                //     let idx = this._messages.findIndex(e => e.id == msg.id)
                //     this.$set(this._messages[idx], 'is_marked', true)
                // }
            } else {
                // let {status} = await ipcRequest('gist/unfavor', msg.id)
                // if (status == 1) {
                //     let idx = this._messages.findIndex(e => e.id == msg.id)
                //     this.$set(this._messages[idx], 'is_marked', false)
                // }
            }
        },

        starChat({id}) {
            this.$emit('startChat', id)
        },

        reChat({id}) {
            this.$emit('reChat', id)
        },

        async copy({content}) {
            this.$copyText(content)
        },

        // 切换折叠 ...
        async switchFolded(params, idx) {
            let folded = params.folded == true;
            // console.log(params)
            this.$set(this._messages[idx], 'folded', !folded)
        },

        async open() {

        },

        // TODO: 重新优化
        edit(id) {
            let doms = document.getElementById(id).getElementsByClassName('jar-editor')
            let dom = doms[0]
            dom.focus()
            let content = dom.innerText;
            dom.setSelectionRange(content.length, content.length);
        },

        remove(id) {
            const idx = this._messages.findIndex(m => m.id === id);
            if (idx !== -1) {
                this._messages.splice(idx, 1);
            }
        },

        cancelChat() {
            // console.log('vava')
            cancelChatgpt()
        },

        quote(content) {
            let str = content + '\n--------------\n> '
            this.$emit('inputPrompt', str)
        },

        start(id) {
            let idx
            if (id == undefined) {
                idx = this._messages.length - 1
            } else {
                idx = this._messages.findIndex(t => t.id == id)
            }
            this.$set(this._messages[idx], 'loading', true)
        },

        end(id) {

            let idx
            if (id == undefined) {
                idx = this._messages.length - 1
            } else {
                idx = this._messages.findIndex(t => t.id == id)
            }

            this.$set(this._messages[idx], 'loading', false)
        },

        isUser(msg) {
            return msg.role == 'user'
        },

        isAssistant(msg) {
            return msg.role == 'assistant' && msg.content
        },

        isSystemOrAssistant(msg) {
            return this.isSystem(msg) || this.isAssistant(msg)
        },

        isSystem(msg) {
            return msg.role == 'system'
        },

        isStarting(msg) {
            return msg.role == 'assistant' && msg.content == ''
        },

        renderHtml(str) {
            return md.render(str)
            // return md.renderInline(str)
        },

        msgClass({role, loading, folded}) {
            let v = loading ? 'z-loading' : ''
            v = folded ? v + ' folded' : ''
            return role == 'user' ? `sent ${v}` : `received ${v}`
        },

        // scrollToBottom() {
        //     if (!this._messages) return
        //     let dom = this.$refs.chatListDom
        //     scrollTo(0, dom.scrollHeight)
        // }

        scrollToBottom() {
            this.$nextTick(() => {
                this.$refs.chatListDom.scrollTop = this.$refs.chatListDom.scrollHeight
            })
        }
    },

    watch: {
        // _messages(ms) {
        //     // console.log('messages : ', ms)
        //     this.$nextTick(() => {
        //         this.scrollToBottom()
        //     })
        // }
    }
}
</script>

<style lang="scss">


.chat-list {

    .rotate {
        animation: rotate 1s linear infinite;
    }

    @keyframes rotate {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }

    width: 100%;
    height: 100%;

    p {
        margin-bottom: 0;
    }

    //border: 1px solid #ccc;
    padding: 10px;
    padding-bottom: 20px;
    overflow-y: scroll;
    background-color: var(--white);
    border-radius: 10px;

    .message.z-loading {
        margin-bottom: 30px;
    }

    .message.folded .user-prompt {
        max-height: 36px;
    }

    .message.folded .md-htm {
        max-height: 36px;
    }

    .message:hover .toolbar {
        display: inline-block;
    }

    .message {

        max-width: 90%;
        min-width: 200px;
        border-radius: 10px;
        font-size: 13px;
        clear: both;
        margin-bottom: 20px;
        margin-top: 30px;
        padding: 0;
        position: relative;
        border: var(--border-in-light);

        //pre {
        //    margin-bottom: 6px !important;
        //    margin-top: 6px;
        //}

        pre {

        }

        //font-family: $fontFamily_editor;

        pre, code, kbd, samp {
            //font-family: $fontFamily_editor;
        }

        //.toolbar-loading,

        &:hover .toolbar {
            //display: inline-block;
        }

        .toolbar {

            position: absolute;
            top: -32px;
            //display: none;

            & > span {
                background-color: var(--white);
                border-radius: 10px;
                padding: 3px 10px;
                cursor: pointer;
                transition: all .3s ease;
                overflow: hidden;
                -webkit-user-select: none;
                -moz-user-select: none;
                user-select: none;
                outline: none;
                border: none;
                color: var(--black);
                border: var(--border-in-light);
            }

            //display: none;

            .badge {
                cursor: pointer;
                padding: 3px 4px;
                //color: $blackColor_L1 !important;

                &:hover {
                    //color: $blueColor_L1 !important;
                }
            }
        }


        .toolbar-side {
            position: absolute;
            top: 8px;

            //.favor.marked {
            //    color:$primaryColor;
            //}

            &.user {
                left: -20px;
            }

            &.assistant {
                right: -20px;
            }

            a {
                i {
                    font-size: 15px;
                }

                &.marked i {
                    //color: $primaryColor;
                }
            }
        }

        .toolbar-loading {

            a.pause {
                //color: $greyColor;
                &:hover {
                    color: #ea5455 !important;
                }
            }

            position: absolute;
            //top: 10px;
            left: 4px;
            bottom: -28px;

            a {
                //color: $primaryColor;
            }

            .badge {
                //padding: 8px 8px;
                //border-radius: 100%;
            }
        }
    }

    .starting {
        padding: 10px;
        font-style: italic;
        font-size: 12px;
        //color: $primaryColor;
        //font-family: $fontFamily_editor;

        svg {

        }
    }

    .user-prompt {
        overflow: scroll;
        min-width: 300px;
    }


    .icon-fold.is-fold {
        i {
            //color: $primaryColor;
        }
    }


    .md-htm {
        padding: 10px;
        //overflow: scroll;

        code {
            padding: 0 !important;
        }

        & > pre {
            padding: 0;
            margin-top: 6px;
            margin-bottom: 6px;
            background: transparent !important;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            color: #6e6b7b;
        }

        table, th, td {
            border: 1px solid #ddd9e9;
        }

        th, td {
            padding: 4px 15px;
            text-align: left;
        }

        th {
            background-color: #EFEFF4;
        }

    }

    .icon-zhedie {
        font-size: 11px !important;
    }

    .sent {
        align-self: flex-end;
        float: right;
        //background-image: linear-gradient(80deg, #7367f0, #9e95f5);
        background: var(--second);
        box-shadow: 0 4px 8px 0 rgba(34, 41, 47, 0.12);
        //color: white;
        order: 1;
    }

    .received {
        align-self: flex-start;
        float: left;
        //margin-right: 30%;
        order: 2;
        background: rgba(0, 0, 0, .05);
        border: var(--border-in-light);
        box-shadow: 0 4px 8px 0 rgba(34, 41, 47, 0.12);
    }

    .tech-info {
        position: absolute;
        margin: auto 0;
    }

    .code-header {
        background: var(--primary);
        display: inline-flex;
        justify-content: space-between;
        width: 100%;
        color: white;
        padding: 3px 6px;
        font-size: 12px;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
        box-sizing: border-box;

        .copy-action {

            cursor: pointer;

            .icon-copied {
                display: none;
            }

            &.copied {
                .icon-copy {
                    display: none;
                }

                .icon-copied {
                    display: inline-block;
                }
            }
        }

    }

    .code-block {
        border-top-left-radius: 0;
        border-top-right-radius: 0;
        margin: 0;
        padding: 10px;
    }

}

.chat-opts-dropdown-menu {

    //padding: 0;

    //background: #E0DFF5;
    border: 2px #E0DFF5 solid;

    .dropitem {
        font-size: 12px;
        font-weight: 500;
        //color: $blackLv3 !important;

        &:hover {
            background: #eef0ff !important;
            //color: $primaryColor;
        }
    }

}
</style>
