const dayjs = require('dayjs')

export function now() {
    return dayjs().format('YYYY/MM/DD HH:mm:ss')
}

// today() {
// return format(new Date(), 'YYYY-MM-DD')
// return dayjs().format('YYYY-MM-DD')
// },
//
// now() {
// return format(new Date(), 'YYYY-MM-DD HH:mm:ss')
//
// return dayjs().format('YYYY-MM-DD HH:mm:ss')
// },
//
// nowTimestamp() {
//     return Math.floor(Date.now() / 1000)
// },
//
// dateExpired(dateStr) {
//     let date = parse(dateStr)
//     let today = parse(DateUtils.today())
//     return isBefore(date, today)
// },

// 两个日期之前的天数
// dayCountBetween(dateStr1, dateStr2) {
//     let date1 = parse(dateStr1)
//     let date2 = parse(dateStr2)
//     return differenceInDays(date2, date1)
// },
//
// 几个月后 ...
// afterMonths(dateStr, n) {
//     let date = parse(dateStr)
//     return format(addMonths(date, n), 'YYYY-MM-DD')
// },

// 运行时间
// runTime(mark, lastTime = 0) {
//     let currTime = Date.now()
//     let spendTime = currTime - lastTime
//     if (lastTime == 0) console.log(`${mark} 耗时：${spendTime}`)
//     return currTime
// }
