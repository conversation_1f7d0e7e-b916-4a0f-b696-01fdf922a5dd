<template>
  <div class="my-agents">
    <!-- 头部操作区 -->
    <div class="header-section">
      <div class="title-area">
        <h2>我的智能体</h2>
        <p>创建和管理您的专属智能体</p>
      </div>
      <div class="action-area">
        <el-button type="primary" icon="el-icon-plus" @click="showCreateDialog">
          创建智能体
        </el-button>
      </div>
    </div>
    
    <!-- 智能体列表 -->
    <div class="agents-content" v-loading="loading">
      <div v-if="myAgentsList.length === 0 && !loading" class="empty-state">
        <div class="empty-content">
          <i class="el-icon-cpu"></i>
          <h3>还没有创建智能体</h3>
          <p>创建您的第一个智能体，开始AI创作之旅</p>
          <el-button type="primary" @click="showCreateDialog">立即创建</el-button>
        </div>
      </div>
      
      <div v-else class="agents-grid">
        <div 
          v-for="agent in myAgentsList" 
          :key="agent.id"
          class="agent-card"
        >
          <div class="card-header">
            <div class="agent-avatar">
              <img :src="agent.avatar || defaultAvatar" :alt="agent.name" />
            </div>
            <div class="agent-status" :class="agent.status">
              {{ agent.status === 'published' ? '已发布' : '草稿' }}
            </div>
          </div>
          
          <div class="card-content">
            <h3 class="agent-name">{{ agent.name }}</h3>
            <p class="agent-description">{{ agent.description }}</p>
            
            <div class="agent-stats">
              <div class="stat-item">
                <span class="label">使用次数:</span>
                <span class="value">{{ agent.usageCount }}</span>
              </div>
              <div class="stat-item">
                <span class="label">创建时间:</span>
                <span class="value">{{ formatDate(agent.createTime) }}</span>
              </div>
            </div>
            
            <div class="agent-tags">
              <el-tag 
                v-for="tag in agent.tags" 
                :key="tag" 
                size="mini"
                type="info"
              >
                {{ tag }}
              </el-tag>
            </div>
          </div>
          
          <div class="card-actions">
            <el-button size="small" @click="editAgent(agent)">编辑</el-button>
            <el-button size="small" @click="testAgent(agent)">测试</el-button>
            <el-button 
              size="small" 
              :type="agent.status === 'published' ? 'warning' : 'success'"
              @click="togglePublish(agent)"
            >
              {{ agent.status === 'published' ? '下线' : '发布' }}
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteAgent(agent)"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 创建/编辑智能体对话框 -->
    <el-dialog
      :title="editingAgent ? '编辑智能体' : '创建智能体'"
      :visible.sync="showDialog"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="agentForm" :rules="formRules" ref="agentForm" label-width="100px">
        <el-form-item label="智能体名称" prop="name">
          <el-input v-model="agentForm.name" placeholder="请输入智能体名称" />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="agentForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入智能体描述"
          />
        </el-form-item>
        
        <el-form-item label="系统提示词" prop="systemPrompt">
          <el-input 
            v-model="agentForm.systemPrompt" 
            type="textarea" 
            :rows="5"
            placeholder="请输入系统提示词，定义智能体的角色和行为"
          />
        </el-form-item>
        
        <el-form-item label="标签">
          <el-select
            v-model="agentForm.tags"
            multiple
            filterable
            allow-create
            placeholder="请选择或输入标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in predefinedTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="头像URL">
          <el-input v-model="agentForm.avatar" placeholder="请输入头像URL（可选）" />
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="showDialog = false">取消</el-button>
        <el-button type="primary" @click="saveAgent" :loading="saving">
          {{ editingAgent ? '保存' : '创建' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'my-agents',
  
  data() {
    return {
      loading: false,
      saving: false,
      showDialog: false,
      editingAgent: null,
      myAgentsList: [],
      defaultAvatar: 'https://ai-v2.deepcity.cn/default/head.png',
      
      agentForm: {
        name: '',
        description: '',
        systemPrompt: '',
        tags: [],
        avatar: ''
      },
      
      formRules: {
        name: [
          { required: true, message: '请输入智能体名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入描述', trigger: 'blur' },
          { min: 10, max: 200, message: '长度在 10 到 200 个字符', trigger: 'blur' }
        ],
        systemPrompt: [
          { required: true, message: '请输入系统提示词', trigger: 'blur' },
          { min: 20, max: 2000, message: '长度在 20 到 2000 个字符', trigger: 'blur' }
        ]
      },
      
      predefinedTags: [
        '助手', '编程', '写作', '翻译', '教育', '娱乐', 
        '商务', '创意', '分析', '客服', '专业', '通用'
      ]
    }
  },
  
  async mounted() {
    await this.loadMyAgents()
  },
  
  methods: {
    async loadMyAgents() {
      this.loading = true
      try {
        // 模拟API调用，实际应该调用真实API
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 模拟数据
        this.myAgentsList = [
          {
            id: 'my-1',
            name: '我的编程助手',
            description: '专门帮助解决编程问题的智能体，擅长多种编程语言',
            systemPrompt: '你是一个专业的编程助手，擅长多种编程语言...',
            avatar: '',
            status: 'published',
            usageCount: 156,
            tags: ['编程', '助手'],
            createTime: '2024-01-20'
          },
          {
            id: 'my-2', 
            name: '文案创作专家',
            description: '帮助创作各种类型的文案内容',
            systemPrompt: '你是一个专业的文案创作专家...',
            avatar: '',
            status: 'draft',
            usageCount: 23,
            tags: ['写作', '创意'],
            createTime: '2024-01-18'
          }
        ]
      } catch (error) {
        console.error('加载我的智能体失败:', error)
        this.$message.error('加载失败')
      } finally {
        this.loading = false
      }
    },
    
    showCreateDialog() {
      this.editingAgent = null
      this.resetForm()
      this.showDialog = true
    },
    
    editAgent(agent) {
      this.editingAgent = agent
      this.agentForm = {
        name: agent.name,
        description: agent.description,
        systemPrompt: agent.systemPrompt,
        tags: [...agent.tags],
        avatar: agent.avatar || ''
      }
      this.showDialog = true
      this.$emit('edit-agent', agent)
    },
    
    testAgent(agent) {
      this.$emit('test-agent', agent)
      this.$message.success(`开始测试智能体: ${agent.name}`)
    },
    
    async togglePublish(agent) {
      const action = agent.status === 'published' ? '下线' : '发布'
      
      try {
        await this.$confirm(`确定要${action}智能体"${agent.name}"吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // 模拟API调用
        agent.status = agent.status === 'published' ? 'draft' : 'published'
        this.$message.success(`${action}成功`)
      } catch (error) {
        // 用户取消操作
      }
    },
    
    async deleteAgent(agent) {
      try {
        await this.$confirm(`确定要删除智能体"${agent.name}"吗？此操作不可恢复。`, '警告', {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'error'
        })
        
        // 模拟API调用
        const index = this.myAgentsList.findIndex(item => item.id === agent.id)
        if (index > -1) {
          this.myAgentsList.splice(index, 1)
        }
        
        this.$message.success('删除成功')
      } catch (error) {
        // 用户取消操作
      }
    },
    
    async saveAgent() {
      try {
        await this.$refs.agentForm.validate()
        
        this.saving = true
        
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1500))
        
        if (this.editingAgent) {
          // 编辑模式
          const index = this.myAgentsList.findIndex(item => item.id === this.editingAgent.id)
          if (index > -1) {
            this.myAgentsList[index] = {
              ...this.myAgentsList[index],
              ...this.agentForm
            }
          }
          this.$message.success('保存成功')
        } else {
          // 创建模式
          const newAgent = {
            id: 'my-' + Date.now(),
            ...this.agentForm,
            status: 'draft',
            usageCount: 0,
            createTime: new Date().toISOString().split('T')[0]
          }
          this.myAgentsList.unshift(newAgent)
          this.$message.success('创建成功')
        }
        
        this.showDialog = false
      } catch (error) {
        console.error('保存失败:', error)
      } finally {
        this.saving = false
      }
    },
    
    resetForm() {
      this.agentForm = {
        name: '',
        description: '',
        systemPrompt: '',
        tags: [],
        avatar: ''
      }
      if (this.$refs.agentForm) {
        this.$refs.agentForm.clearValidate()
      }
    },
    
    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.my-agents {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  
  .header-section {
    background: white;
    padding: 24px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .title-area {
      h2 {
        margin: 0 0 4px 0;
        font-size: 24px;
        color: #333;
      }
      
      p {
        margin: 0;
        color: #666;
        font-size: 14px;
      }
    }
  }
  
  .agents-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    
    .empty-state {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .empty-content {
        text-align: center;
        
        i {
          font-size: 64px;
          color: #ddd;
          margin-bottom: 16px;
        }
        
        h3 {
          margin: 0 0 8px 0;
          color: #333;
        }
        
        p {
          margin: 0 0 24px 0;
          color: #666;
        }
      }
    }
    
    .agents-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;
    }
    
    .agent-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        
        .agent-avatar {
          img {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
          }
        }
        
        .agent-status {
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          
          &.published {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
          }
          
          &.draft {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
          }
        }
      }
      
      .card-content {
        .agent-name {
          margin: 0 0 8px 0;
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }
        
        .agent-description {
          margin: 0 0 16px 0;
          font-size: 14px;
          color: #666;
          line-height: 1.5;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .agent-stats {
          margin-bottom: 12px;
          
          .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
            font-size: 14px;
            
            .label {
              color: #999;
            }
            
            .value {
              color: #333;
              font-weight: 500;
            }
          }
        }
        
        .agent-tags {
          margin-bottom: 16px;
          
          .el-tag {
            margin-right: 8px;
            margin-bottom: 4px;
          }
        }
      }
      
      .card-actions {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        
        .el-button {
          flex: 1;
          min-width: 60px;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
