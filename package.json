{"name": "xiaoyi_pc_ui", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@kangc/v-md-editor": "^1.7.12", "axios": "^1.6.5", "cash-dom": "^8.1.5", "core-js": "*", "dayjs": "^1.11.10", "element-ui": "2.15.14", "js-cookie": "^3.0.5", "jshashes": "^1.0.8", "lodash": "^4.17.21", "mobile-device-detect": "^0.4.3", "nanoid": "^3.2.0", "sass": "^1.69.5", "sass-loader": "^13.3.2", "v-wave": "^1.5.0", "vue": "^2.7.0", "vue-carousel-3d": "^1.0.1", "vue-clipboard2": "^0.3.3", "vue-codejar": "^0.1.1", "vue-feather-icons": "^5.1.0", "vue-router": "3.4.9", "vue-virtual-collection": "^1.5.0", "vue-waterfall": "^1.0.6", "vue-waterfall-easy": "^2.4.4", "vue-waterfall-plugin": "^3.3.1", "vue-waterfall-rapid": "^1.0.25", "vue-waterfall2": "^1.10.6", "vue2-waterfall": "^3.0.1", "vuex": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "main": "index.js", "repository": "https://e.coding.net/g-pcxj7966/ycyz/xiaoyi_pc_ui.git", "author": "杨文志 <<EMAIL>>", "license": "MIT"}