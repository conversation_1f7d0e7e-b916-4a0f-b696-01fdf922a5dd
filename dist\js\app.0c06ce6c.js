(function(){"use strict";var t={8901:function(t,e,i){var s=i(6848),a=function(){var t=this,e=t._self._c;return e("div",{attrs:{id:"app"}},[e("router-view")],1)},o=[],n=function(){var t=this,e=t._self._c;return e("el-container",[e("layout-header"),e("el-container",{staticStyle:{"padding-top":"52px","padding-left":"4px"}},[e("layout-sider"),e("el-container",[e("router-view")],1)],1)],1)},l=[],c=function(){var t=this,e=t._self._c;return e("el-aside",{staticClass:"layout-sider",attrs:{width:"76px"}},[e("div",{staticClass:"menus"},[e("div",{staticClass:"item",class:"chat"==t.pageName?"active":"",on:{click:function(e){return t.goLink("chat")}}},[e("div",{staticClass:"icon"},[e("img",{attrs:{src:"chat"==t.pageName?t.iconPath.chatOn:t.iconPath.chat}})]),e("div",{staticClass:"text"},[t._v("AI 对话")])]),e("div",{staticClass:"item",class:"draw"==t.pageName?"active":"",on:{click:function(e){return t.goLink("draw")}}},[e("div",{staticClass:"icon"},[e("img",{attrs:{src:"draw"==t.pageName?t.iconPath.drawOn:t.iconPath.draw}})]),e("div",{staticClass:"text"},[t._v("AI 绘画")])]),e("div",{staticClass:"item",class:"tools"==t.pageName?"active":"",on:{click:function(e){return t.goLink("tools")}}},[e("div",{staticClass:"icon"},[e("img",{attrs:{src:"tools"==t.pageName?t.iconPath.toolOn:t.iconPath.tool}})]),e("div",{staticClass:"text"},[t._v("AI智能体")])]),e("div",{staticClass:"item",class:"cooperation"==t.pageName?"active":"",on:{click:function(e){return t.goLink("cooperation")}}},[e("div",{staticClass:"icon"},[e("img",{attrs:{src:"cooperation"==t.pageName?t.iconPath.cooperationOn:t.iconPath.cooperation}})]),e("div",{staticClass:"text"},[t._v("协作空间")])]),t._l(t.urls,(function(i,s){return e("div",{key:s,staticClass:"item",class:t.nowCategory==i.guid?"active":"",on:{click:function(e){return t.getMerchantPcTools(i.guid)}}},[e("div",{staticClass:"icon"},[e("img",{staticStyle:{width:"32px",height:"32px"},attrs:{src:i.categoryIcon}})]),e("div",{staticClass:"text"},[t._v(t._s(i.categoryName))])])}))],2),t.isPop?e("div",{staticClass:"other-url-pop"},[e("div",{staticClass:"content"},t._l(t.otherUrls,(function(i){return e("div",{key:i.guid,staticClass:"item",on:{click:function(e){return t.onOpen(i.urlLink)}}},[e("div",{staticClass:"icon"},[e("img",{staticClass:"img",staticStyle:{width:"32px",height:"32px"},attrs:{src:i.urlIcon}})]),e("div",{staticClass:"text"},[t._v(t._s(i.urlTitle))])])})),0)]):t._e()])},r=[],d=(i(4114),i(8355)),h=i(9143),u=i.n(h),p=i(782),g=new p.Ay.Store({state:{chatItems:[],chatIdSelected:null,merchantGuid:localStorage.getItem("merchantGuid")||"",userGuid:localStorage.getItem("userGuid")||""},mutations:{setMerchantGuid(t,e){t.merchantGuid=e,localStorage.setItem("merchantGuid",e)},setUserGuid(t,e){t.userGuid=e,localStorage.setItem("userGuid",e)},clearMerchantGuid(t){t.merchantGuid="",localStorage.removeItem("merchantGuid")}},actions:{},getters:{chatItemSelected:t=>t.chatItems.find((e=>e.id===t.chatIdSelected))}}),m=i(8704);const v="session";function C(){return m.A.get("token")||""}function A(t){m.A.set("token",t)}function f(){return m.A.remove("token")}function y(){let t=m.A.get(v);return t?JSON.parse(t):void 0}function b(t){return m.A.set(v,JSON.stringify(t))}var w=i(8626);const I=function(t,e,i,s){let a=[],o=[],n=[];if(t)for(let h=0;h<=9;h++)a.push(h);if(e)for(let h=65;h<=90;h++)o.push(h);if(i)for(let h=97;h<=122;h++)n.push(h);if(!s)return void console.log("生成位数必传");let l=a.concat(o),c=l.concat(n),r=c.length,d="";for(let h=0;h<s;h++){let t="",e=x(0,r);c[e]<=9?t=c[e]:c[e]>9&&(t=String.fromCharCode(c[e])),d+=t}return d},x=function(t,e){var i=t,s=e;return t<e&&(i=e,s=t),parseInt(Math.random()*(i-s))+s},k=function(t,e){if(t){let i="";for(let e in t){let s=t[e];Array.isArray(s)&&(s=s.join(",")),i+=`&${e}=${s}`}return e.indexOf("?")<0&&(i=i.replace("&","?")),i}return""},R=i(8189),P=new R.MD5,M=new R.SHA1,L=t=>{let e,i=t,s={app_guid:i.urlSuffix.app_guid,app_type:i.urlSuffix.app_type,token:i.urlSuffix.token},a=P.hex(JSON.stringify(s));if("POST"===i.method||"post"==i.method){let t=i.data?P.hex(JSON.stringify(i.data)):"";e="POST\n"+t+"\n"+a+"\napplication/json\n"+i.urlSuffix.expires+"\n"+i.urlSuffix.noncestr+"\n/"+i.url.toLowerCase()}else e=i.method+"\n"+a+"\n"+i.urlSuffix.expires+"\n"+i.urlSuffix.noncestr+"\n/"+i.url.toLowerCase();return M.b64_hmac(i.urlSuffix.token,e)},S=d.A.create({baseURL:"https://ai-api.deepcity.cn/",timeout:2e4,headers:{"Content-Type":"application/json"},transformRequest:[function(t){return JSON.stringify(t)||{}}]}),D=d.A.create({baseURL:"https://ai-api.deepcity.cn/",timeout:2e4,headers:{"Content-Type":"application/json"},transformRequest:[function(t){return JSON.stringify(t)||{}}]}),T=t=>(t.urlSuffix={app_type:"xiaoyipc",app_guid:"xiaoyipc",expires:(0,w.parseInt)(((new Date).getTime()/1e3).toFixed(0)),token:C()||"",noncestr:I(!0,!0,!0,32),merchantGuid:g.state.merchantGuid},t.urlSuffix.signature=encodeURIComponent(L(t)),t.url=t.url+k(t.urlSuffix,t.url),t);function E(t={}){return S.post("square/api.chat/createLunci",t)}async function B(t={}){let{data:e}=await S.post("square/api.chat/nowLunci",t);return e}async function O(t){let{data:e}=await S.post("square/api.chat/lunciList",t);return e}async function U({startId:t=0,page:e=1,chatLunciGuid:i,is_all:s=1}){let{data:a}=await S.post("square/api.chat/history",{startId:t,page:e,chatLunciGuid:i,is_all:s});return a.reverse()}async function V(t,e){return await S.post("square/api.chat/updateLunci",{chatLunciGuid:t,chatLunciTitle:e})}async function G(t){let{data:e}=await S.post("square/api.chat/saveMsgV2",t);return e.msgId}async function F(t){return await S.post("/square/api.chat/lunciDelete",{chatLunciGuid:t})}async function N(t){let{data:e}=await S.post("/square/api.chat/ableModel",t);return e}async function q(){let{data:t}=await S.post("/square/api.chat/chatRobotList");return t}async function z(t){await S.post("square/api.chat/clearLunci",{chatLunciGuid:t})}async function j(t){let{code:e,data:i,msg:s}=await S.post("/square/api.chat/chatBuildImg",t);return 0!=e?((0,h.Message)({message:s,type:"error"}),null):i.url}async function H(t){let e=await S.post("/user/api.userWork/videoCreate",t);return e}async function X(t){let e=await S.post("/user/api.userWork/videoResultQuery",t);return e}async function Q(){let{data:t}=await S.post("/square/api.chatGoods/index");return t}async function Y(t){let{data:e}=await S.post("/square/api.chatGoods/buy",{chatGoodsGuid:t,payEnv:"alipay_pc"});return e}function _(t){return S.post("/square/api.chatGoods/buy",t)}async function J(t){let{data:e}=await S.post("/merchant/api.index/pcToolsUrls",t);return e}async function W(t){let e=await S.post("/merchant/api.index/pcToolsUrlCategory",t);return e}async function K(t){let e=await S.post("/square/api.chat/collectionChatSave",t);return e}async function Z(t){let e=await S.post("/square/api.chat/helpRoleList",t);return e}async function $(t){let e=await S.post("/square/api.chat/addHelpRole",t);return e}async function tt(t){let e=await S.post("/square/api.chat/editHelpRole",t);return e}async function et(t){let e=await S.post("/square/api.chat/deleteHelpRole",t);return e}async function it(t){let e=await S.post("/square/api.chat/exitHelpLunci",t);return e}async function st(){let t=await S.post("/square/api.chat/aiVendorList");return t}async function at(t){let e=await S.post("/square/api.chat/aiVendorModelList",t);return e}async function ot(t){let e=await S.post("/square/api.chat/chatLunciHelpInfo",t);return e}async function nt(t){let e=await S.post("/square/api.chat/lunciHelpUserList",t);return e}async function lt(t){let e=await S.post("/square/api.chat/lunciSelectHelpUser",t);return e}async function ct(t){let e=await S.post("/square/api.chat/lunciOwnerUpdateStatus",t);return e}async function rt(t){let e=await S.post("/square/api.chat/lunciHelperUpdateStatus",t);return e}async function dt(t){let e=await S.post("/square/api.chat/lunciPromptSave",t);return e}S.interceptors.request.use(T,(t=>(console.log(t),Promise.reject(t)))),D.interceptors.request.use(T,(t=>(console.log(t),Promise.reject(t)))),D.interceptors.response.use((t=>{if(704001==t.data.code)return f(),h.Message.closeAll(),(0,h.Message)({message:"登录失效,请重新登录。",type:"error",duration:2e3}),void(void 0).$router.push("/");const e=t;return e.data}),(t=>((0,h.Message)({message:"访问异常，请稍后重试",type:"error",duration:5e3}),null))),S.interceptors.response.use((t=>{if(704001==t.data.code)return f(),h.Message.closeAll(),(0,h.Message)({message:"登录失效,请重新登录。",type:"error",duration:2e3}),void(void 0).$router.push("/");const e=t;return e.data}),(t=>((0,h.Message)({message:"访问异常，请稍后重试",type:"error",duration:5e3}),null)));const ht=t=>S.post("/square/api.chat/historyHelpRoles",t);var ut="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAAgZJREFUSEvtls9LFGEYx7/Puwst0UHqIhF0NaiD1B8w0iV2lljHoHJfIcG9dQkKOwR1i45hHQIjwXf3UjMkzE5EHQZE8OZCgR6CLgWdxEBJbX2e2I0FxR3x3dZU8D0NzMN85vt9fr2EfTq0T1wcgf+b8wfPajc/eJEUGRD12NogIjUAZmXxezGO4/rztpOoOOcV5gGqschzkKzawBXUWYE8ENDtKDAv7MADWlhQjAIzbgNtxrpe4SuBPoaBKbYB5uEoKE+0CwYhrvil4cMJzg1oSVLOst2ZutUdUZzz9MNEMPNU9LZc3fy+Y2DbPB9+cKscC2QiqWpznp4WwmzFN/f+qapb5ZgVV6M35anmh518vgtLS8tJ02rzDyRPrsYAsetj19NjRPgS+uZpE5Ltv3lFqdRo6Ju+PQG7/dfPg9JzBFrm1bVzUfT6Rx2U9QZvKVKvQt9sEdkxxa5X+PBXEXWDUK34ZmjPwa6nXSKEvMG9oiijgGkGnHdBaaYtxSI8UgnKL3fqYcdxMidOnfkkwCyEYyLVWCoi+Lyy+K33+MnT2s7q5loEnlGLtSi/1t/X8+he06MQPKr9xoVUGpcV0A1IWoD7RHSXhX9agbNXb1xS6dRk4kVgA328vragMsfmG/0clO5sHZl6jADNIk+Uose7Li7bEWkbf/DuXLYKbOOPFNs61nb8H6jSSC7rhjooAAAAAElFTkSuQmCC",pt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAArlJREFUSEvt1k9IFFEcB/Dv77mVhw7hrotJVBchKw+ah7qtBCFE0aHdnV00HGg75SFQ7GAlWETYRbYOYbGC/1bGi9hF7DAhkRCYYGaHhY0uaT6lwEPY7vvF7I6xYSs7e0gPzmUZ5r35vN/3zb73CDt00Q652IP/W/K7L+pe/8oZwTRIwAmnMTCQYsbg2up8pMtsSP2rf96KH1+ViyCkhOKnzPzTCS5EybE0uJMhbraPlT1zBN/3S1bMkbtj5c+doJttH/hlkpledY65I47gTr9kgtK7DW9/MfCdgEyCYXYbHt0R3GbDPUXC7RZMMHtGHcKtAclgpUdtOHOf78ppt9mk1YajTuFIBobeZ3gyUd8IrN7L63J6vM/wzuU+jwRlEoDZ5xRuDkqGUvpAkVFfC8okA+aAU9hvwaR0I17cxxWwYcMpfEmz5lTpEzacuWeACNZP9mL0T+R58WVtdZqBmYm4u93RV31Bk8yAPhXPznGjtnWOmXluctQzvvli35XkIXz/vG7mWa1yB5B35fLZFZsOovZpMgpBCXPY3ftnMKFvjczU8Tpe3lAQfC5TsdJnCoTrg0un95HrPRHWf6VU9TvDu2RBZzXZQoTY2xHPX0XmrbgulP0fzxYI14XklHWsIEYFA3OzI55mC67TZAsIsdlC4VMhyczQP9pzvN2yWa2tXRSkXnJK1bIoKS0RPM1C+BaGyt6ctCteKBSuCltR0/XEsPvFduhxX7LUVXlwHkQzYDIBzm4qjA+Jr/O1VZU1TQzEEsMFRn00LBeZkFKKn0DQlm1xY0NNrhjepSNh2SGIu9QG17gOuM4rxRUEdingNoHaGPyDgNiXQuHD2ko9CxrgPAeBtOIGSvMnsV8sAtS/POS+lZtMRVhGmdCkFB4JwsPlQuFitkInfXbfmcvJ6Itpu1dxMakV1ec3mx1HLpyRPEoAAAAASUVORK5CYII=",gt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAArZJREFUSEvtlk9IVEEcx7+/cUMzD2JEUlLdKihI6tIheIsX27fa+jQq35MU8uYlKvQQ6CEIqUtoRGAk+dYEe6uH3c2ow4JEQkFCgR2COmh0Wg120bXd+cUa5mpu+J5uenBOMzAzn/l9f/+GsEmDNomLbfB/U37rSa366k+SIBNER+zKwMxJAGY8OtUciUTS879GVou9mj4BUFIy3wfxnB24gDjI4JsMagkHzIf2wLUGS0ZzOGD22IEu7lU1/QuBXgUDZrMDsGwKB/p7nYJBiIQsf9OGgxVFcRXtObA3OPhkauXlaYtzBlZr9T4CfDJFjeFh08qE5wzs0erbBagFoG4mtBHQFrTMe5k+3nCL1Rr9Mgg9xNIdm06O7yrJf0PEX4OWvypnYK9Pr2CBETA3xKe/PSvavX+ImQ7Fo4nTkchgLCfgymrjsGsHv2NJt0JDZqe3Vn/AIB/PJsrD4cHvmT72asYoE8ZClnljXVHt8ZwvpZ35b4kxks5NVdOvEaFDpvhMeLh/PH254vMVY2Ymlq1aZT4ge+VaKCBLeaxqRhcBJ2LRSXdhcdk5EjzALKvCQ09HluQ1uojwOTPIPDWXKoXIaw1aptsR2KsZ7QBXgmGyoLtg2RIK9D/6A625cAzkek+gmJxLHF2U3qPVNwoSj4OWuczINVt8VtPrBHAHhBlIGkj7eEXevvy9plIQxkOW2ZBerRv8r7KpaoZKhKBMyXIWVCCAUQkozwP+147AzPJKppyrwRVFKSjaXfaBgTGwjBCJhabCjI/x6GR5Yck+w57Ui20R6KZV2iLPzr9I+1GtM1rB6Ej+xPE8FyoEUAqwi4E2IrouWf6wBfZUXzwlXHl9WT8CKbjlfOKTKMifYHBvKOC/utznC1lgSOZOIej2moPLSSu0c2br/bnsvN7J3m2Lnajm6Mwv0u2ZLqWZNXgAAAAASUVORK5CYII=",mt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAA25JREFUSEvt1k9oXEUcB/Dvb3Ztc/Ag2c02LaK9FK0aMTGH9iDsUpSApOaQ3bzERvOge0sPYkNziDbQioQUpCQi0pYUkyYbX8RDRFIi+KRIAtUYTGs8BLYI0vyZLRVyKHF3vrLJLt1qNuzbg+2h7/pm5vO+v5k3M4KH9MhDcvEY/t8q/+iV+nx07VVFGRHgea9lIJAmMXIntRDvdSPp7foXTXyuWS9CkFaGn5K85wVXyvdsBuwhVGfXROXnnuCzUU1Dxj+cqLroBc23/Siqk6R81zMRiHuCe6KaAmOfcUKXy4E/iOkkCPeME7Q9wSdzcP8OcDj8vb+2qmbPJ07wz38P3pWFBW7/uEf4REwTNPbADnBnTA8DaAKkY/DLwFeF+IkcPOAVjm/CsC84wW1LfTyWOq3EdIJqkMJuA+m+NB44n8fjLToJwL3gFW5v0YQx9vA2id+O6XcVcBE0kXvAfIVSMwLc+mI82JiH32nRSQLusFc4moXF2E7iwcXVbOkjQkwpsH1l5cZEcE/N1wqynyZz2HFC63k4loMdr3CjpQkYe7IAboiuPbdL4SeK7+xkorLvqKU/A9GUNqb2Wye0XDjHR63UNQKzk4lAl6dV/YalScCeTmzNcTi6Wr3br64DMnV1LBB/vTX1viJ7Tca8Nu2E5jfbNCWfwt1b626R3arwA4ruXOFcYjeXOGzpAYi8gtu/RjJ7X37LRyYgptEdC03lB9xso2TJHb2/yMKtqw2knPohURUpCT68mdjYszn4kJU6DbCBwIhPeI5UnTOJwKX8YPUtyy89If5fRLD+d9ocvJ4r/SFLd4hgaGYs+EDIoonrWrf+47kcXG+tNhvx9YvgLiCJn0cr+woT1LXq6ey1QohqAvNzY8H27Ps6S3dAMDRXKvxiqyYJ+7fcHO+0bR607rypxHzDtKml8lX4FK9RqfDNK5U/vpBLfLNU+EBbttRyfGn0fjm3w/eHkxX+fU8uQGQWFBfg1qFC3Fi6vVB7YF/NMQJDS6MllvqZNr1IQdoYDkLJf47FjQ1zdc0JLT/dpk8pYa/ZYI1/t/+IMawW0G+AboGcJPiXAEN/lArvtdbqqWSYRS4CGcOIZPi72qUWAbm8ciXwXmFFqtv0AAXHjEGfEny8UipczlHopc+jd+fy8vXltH2cuJyqldXnH1oTkS4NBUOlAAAAAElFTkSuQmCC",vt="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAAi1JREFUSEvtlsFr1EAUxr83G3ARD6IeFhG8Kuih6B+QogVJtrJNFe1uBIvuzYug1IMHb+JRqgfBYsHZ9SDJpUlELBgoQm8KCvUgeFHwIKVCi9u6O0+yEFzrppq0tT3sXJIM781v3jcv7w1hiwZtERc98H9TfvtJbZbKx0iQBNGhtDIwcxOAXJr/XA3DMHr/YyRGXLQqcwA1FfN9EDfSwAXEQQbfZNCVwJUP0oGHbVaMauDKh2mgsa1pVT4SaNpzZTUDWI0Gbn0yKxiE0Hdqo+sGDw6O7FNa7hIR8p2LMaMhmq2JqaknXzsj3jCwaZVPEokX3SJgVgO+W5/eFHAnsDhsv4y+PUf2d9tIdMYbFnEPvJbURcueYcKs78jr687qv0mtl0q7sbCwmFStOv2TK1e7gCT/x6ZVaWe379YGfmWyPU6ED54j78ZzxtDIKSFyY6uTMDPYMM4WosWD4OmX6GkOnTsC0l4TaFE1lg/H84ZVvihIPPIc+RsrM3j1ucUKAFQA4Y3vyAuRzaaCTcs2ieCplupjQXkBzChAf+bWXmUCM6vLvlufWKtW67qe37X3wFsGZsEqJBLtpsKMd0vzn/p27tlvp5M6bovAPerSFvn7yvPoHM0z9hgYt5o/cDSn4YQACgBrDNwgomuK1bdUYOP0+eNCyz1OvAi00K9Wlt+L/I45Bk/6bu1qpzKmZY8TYCvmO0LQ7X9OriytMI3P9rtzpdl9FttexFlUy+TzE3zwWS6DT+CBAAAAAElFTkSuQmCC",Ct="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAAu5JREFUSEvt1k9IVEEcB/Dvb9zKoEO4q5lEdRGy8qAZ1G2lrCCMDq37FA0ftbc8RIodLASLCOsgW4cwUfDf2nqziyj4QiIhsMDMDsJGh/LPKAVCYrvzi6e7tda+2LeH9OC7LMzOzGd+v9+8mUfYoIc2yMUW/N8yv/lS3eKZPyaYugg4ZDcNDISZ0bW4MOFrNIrDicZbRvzgkpwCISwUP2bmZTu4EGkHIuAGhrhW15/xxBZ8xyNZMftu92c+tYPG+t71yBAzDTf0O3224AaPZILSm4JZHanAt8pkCAyjKejSbcG1Ubg5Dr5R+tlFO7dfUaD0+MkEeJm/r7Q9HMiRsfY6EyYYzX024ZoyyWCl++PgmjJ5GsBQ4gxESvzP9gzH/quJwn67sG8Vht4adCVMtc8rR0yktc9VnGghPq8MATBa7cJVXslQSu+0qHFVFO60gC97ZYgBo9Mu7DFhUnowkHhzeaJw0AIui8JBu3CpJhlQ+oAFXKrJETAwYAFf0BZGGRgbCDjrbO3qM5pkBvShQOIanzVhAIOB3zV2XwztxtePS4bFaRW/AMuTyx2N2LCI2F0uV3e30esqiU3o1qQfgqaNHmfLr7byuXPMVP8ikLluE1rCJ1cjVvqYBXzcM5dtTv46mDVj/hZ5Z45uI8cbIiz9CKu8WPsJTVYTof1Vr2udZQkXlq+9x+MW8J91KzQzQAAxshl4O97rqjL7FGqyGoT28WThI+WSmaG/t6hxPJynLZ4XpJ5zWBWwSEtPEzzKQrgnuzNeHo5GPJksnFthppquTvc42/51Vh90h9IdObsmQDQGJgPgtUuF8W76y0RBbk5+JQPt0z1Jpnp/hZxiQlgpfgRBf12LKytqcD6YNbOvQtYL4ka1wvmOHY5TSnE2gR0KuEmgWgZ/I6D9U7LwXm2+iAV1ssWHQERxMUX4g9gupgDqmO12Xo/PTHaF9DOhUincF4R7s8nCqVyFdsZsvm8uO6tPpe9WxKlkLaUxPwFGRlYutquPVQAAAABJRU5ErkJggg==",At="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAAqlJREFUSEvtlk9IFHEUx7/vt7O62BKSh5YSuhQYFCR5DBopShxb3LXI1REUEjp4CQo9dOgQRMewiKDIaHYTYseFdlejDgMSCAYFBgYFFhR5sg2U/De/V7OyoKXSTFsZ+DvNML/H5/d98973/Qj/aNE/4mIT/Ncyv/FSrTW2HCRBBoiq3KaBmRcBGDNTHzsty3Kef1prKm6Ito4DtCiZb4B41g1cQOxi8EUGdWVN45Y7cJPOktGZNY3bbqCFvVq0dYJAT9Om0ekBLDuyZqLPKxgEK5OMd/zf4HA4tl36fEclIQjIoayZeL+aIifVRVN8ItJ2mIVMAxR0YE71ClDXo1UKqGhgVVUDWyoqJwick7Db7AU7p/hLboKh2tLeM5Tqf7dcedHAdZHYIUX4hm3bjgymHqQcyPHwqd1+f+kbyTj7Y9sUDVwfbq4RfmWUwe2ZZPyeA65vaq4RUEZZylhmING/XHFDVB9mwkgmaVz4rapWVVUJVuwcY1A5JPewoGkBvgymkJyd25vNPpxUGxvLkctNr+VWyw+wtnPlDWRlHx9rilWVQAwASzbKwCTZiKVThuW8a1G9lwhv00njWgFSH4nVCeHrTieNWs9gJ9BRXrY1dED6KDD7+dNzy7LydqpFTu8DKS8INF3IQP53RFvaBYm76aSxQqQrxes5mBZtfbL0nUIgvMwkjbY/DtaiukaEtLRlNQsKCGBYAuqgGX/mSTGzPJMxE3fWU+r0d7CicoyBEbC0iER+qDDj1czUh+qybTt0d6kujEXgOq0yFvnr/GOnkrWTevf3Kru0uID9PgVHBBACWGGgh4jOS5ZfXIHzfav47q95EbBRK+fnXotA6TiD+zJm/NxK59J7CdAl81Uh6MovF5eXUegmZuPdudyc3sveTcVesuYp5htZbo4u1pZDpAAAAABJRU5ErkJggg==",ft="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAA1dJREFUSEvt1k9oHFUcB/Dv72W1OUSo3c12W/xXMNBoAzbmoCA4QRFRLBbcZDY0kgEXPLSI0NAeohRaEamXEotILamkTTbdoKXtQWmhI0EMRKIQawQDm/bipvtaI6RUdmffV3Yz0TXtlp092B46x5n3+Mz3N29+7wnu0CV3yMU9+H+r/N1X6kPx3NOKclyAzUHLQMAjcfza1ZnkPrfTu9X8qok/fkPPQuApw8Mk/wqCK9XwaBEcINTO/vF1nwWCD8Q1DZl8f7z58yDoytgP4jpDyvmB8XAyEDwQ1xQYZ386eqwe+L0unQHh7k9HnEDwbh8+WCfcX4IF7sGxgPCuLk3QOIOr4HcSS+sL5saLQEOTFOTrw18+eOlWiXb58GBQOFmG4RxJR/4pdTL+x/MQ7ywgTT7mGVE7j47dvICS3ToDwD0SFO7t1oQxzrCf2LIyjQ9HH8iIYJEee6m4qJT6lIRVyBdaUqc2zFcmf7NbZwi4w0HheAkW46RTy4srHr/yHJSaEBa3nzy5/pR/73Eo9Ruh3h5flbrLh9NB4ddsTcA4Z3x4m53rIGVKFPpOj0a+KMGv2LmOEGQKZOL0WHOqMvE2++oEgckzqXB/oFX9kq1JwDmXWv7GlnUhtCbWNgNyLZXsNZAlRR4AEcsb0+qmo1nr9cxaLM4vuVW6VeULVO1clp/Y9ROX8YTeDOIr/NtGswATbqrZLT+39SCUzLkj4UMriJW48jIpe75NNXfWBD9bTmycyQp4JXkhtuUpr8jGhdz1H+bdTeV22tGd3XKfhH4UwVLBM61T6Wi2dP8ZW/eJYOj70ch/QlZN3J5Y/o+nV8HVulh7Qp8rHSuEiBH4aXo00lsa227rPgiGpmuFn0xoknB+8b/x7dpmq33tVSXmLD2zlaqhsUFxgkpZF0+s++4JP/HFWuGWnlKp5a25kfDR26GPWZnG0MamGYhMguICXN5UiJ/nfp/Z2rKxbQeBobmRGkv9SI+epcAzhp9AyU3bYj5vvsmlo9mHevQeJdxn8mwLrQm9YAxjAoYMsFcguwn+KcDQ5VrhDaX/VskwqxwEioadUuSv6n41C8ixhRPhdysrE+vRgxTsMAYfKcGHC7XC9WyFQebcfWeuIG9fz9h7ieupWl1z/gYTrIsuS/B06QAAAABJRU5ErkJggg==",yt={name:"layout-sider",async mounted(){this.pcToolsUrlCategory()},data(){let t=this.$route.name;return{urls:[],otherUrls:[],nowCategory:"",isPop:!1,pageName:t,iconPath:{chat:ut,chatOn:pt,draw:gt,drawOn:mt,tool:vt,toolOn:Ct,cooperation:At,cooperationOn:ft}}},methods:{goLink(t){this.isPop=!1,this.nowCategory="",this.$router.push("/app/"+t)},async pcToolsUrlCategory(){let t=await W({merchantGuid:this.$store.state.merchantGuid});this.urls=t.data},async getMerchantPcTools(t){if(this.nowCategory!==t){this.nowCategory=t,this.isPop=!0;let e=await J({merchantGuid:this.$store.state.merchantGuid,categoryGuid:t});this.otherUrls=e}else this.isPop=!1,this.nowCategory=""},onOpen(t){this.isPop=!1,this.nowCategory="",window.open(t,"_blank")}},watch:{"$route.name":function(t,e){this.pageName=t}}},bt=yt,wt=i(1656),It=(0,wt.A)(bt,c,r,!1,null,null,null),xt=It.exports,kt=function(){var t=this,e=t._self._c;return e("div",[e("el-header",{staticClass:"layout-header center",attrs:{height:"52px"}},[e("div",{staticClass:"logo"},[t._v(t._s(t.pcConfig.title))]),e("div",{staticClass:"flex user-box"},[e("div",{staticClass:"btn-buy-tc",on:{click:t.showPayMeal}},[e("img",{staticClass:"icon",attrs:{src:i(9929)}}),t._v(" 购买套餐 ")]),e("div",{staticClass:"btn-buy-vip",on:{click:t.showVipDialog}},[e("img",{staticClass:"icon",attrs:{src:i(7960)}}),t._v(" 购买会员 ")]),e("el-dropdown",{directives:[{name:"show",rawName:"v-show",value:t.session,expression:"session"}],staticClass:"user-dropdown",attrs:{trigger:"click"}},[e("span",{staticClass:"flex-center"},[e("img",{staticClass:"header-img",attrs:{title:"点击修改头像",src:t.session.headImgUrl}})]),e("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[e("div",{staticClass:"user-center-menu"},[e("div",{staticClass:"logo-box"},[e("div",{staticClass:"logo"},[e("img",{staticClass:"header-img-large",attrs:{src:t.session.headImgUrl}})]),e("div",{staticClass:"name-box"},[t.isVip?e("div",{staticClass:"vip"},[e("img",{staticClass:"img",attrs:{src:i(8839)}}),t._v(" "+t._s(t.bigVipInfo.cardInfo.cardName)+" ")]):t._e(),e("div",{staticClass:"name"},[t._v(t._s(t.session.nickname))])]),e("div",{staticClass:"edit-box",on:{click:t.showModifyUser}},[e("i",{staticClass:"el-icon-edit"})])]),e("div",{staticClass:"card-box"},[e("div",{staticClass:"point"},[e("span",[t._v("算力余额："+t._s(t.session.chat_count))]),e("span",{staticClass:"meal",on:{click:t.showPayMeal}},[t._v("去充值")])]),t.isBigVip?e("div",{staticClass:"qy-box"},[e("div",[e("div",{staticClass:"label"},[t._v("会员权益生效中")]),e("div",{staticClass:"date"},[t._v("权益有效期："+t._s(t.bigVipInfo.vipInfo.cardEndTime))])]),e("div",{staticClass:"btn",on:{click:t.onGotoBigvip}},[e("img",{staticClass:"img",attrs:{src:i(4887)}}),t._v("权益中心 ")])]):t._e()]),e("div",{staticClass:"item-box"},[t._v("联系客服")]),e("div",{staticClass:"item-box flex space-between"},[e("span",[t._v("分享得算力奖励")]),e("span",{on:{click:t.copyPath}},[t._v("复制链接")])]),e("div",{staticClass:"item-box",on:{click:t.logout}},[t._v("退出登录")])])])],1)],1),e("el-dialog",{attrs:{id:"modify-user",title:"修改个人信息","append-to-body":!0,visible:t.modifyDialogVisible,width:"30%"},on:{"update:visible":function(e){t.modifyDialogVisible=e}}},[e("div",{staticClass:"flex-center"},[e("el-upload",{staticClass:"avatar-uploader",attrs:{action:"https://ai-api.deepcity.cn/user/api.userinfo/uploadImg","show-file-list":!1,name:"img","on-success":t.handleAvatarSuccess,"before-upload":t.beforeAvatarUpload}},[e("div",{staticClass:"relative"},[e("el-image",{staticClass:"header-img",attrs:{title:"点击上传头像",src:t.modifyHeaderImg}}),e("div",{directives:[{name:"show",rawName:"v-show",value:t.uploading,expression:"uploading"}],staticClass:"uploading flex-center"},[e("i",{staticClass:"spin el-icon-loading"})]),e("a",{staticClass:"btn-edit",attrs:{title:"点击上传头像"}},[e("i",{staticClass:"el-icon-upload2"})])],1)])],1),e("div",{staticClass:"flex-center mt20"},[e("el-input",{staticStyle:{width:"60%"},attrs:{placeholder:"请输入昵称"},model:{value:t.modifyNickname,callback:function(e){t.modifyNickname=e},expression:"modifyNickname"}})],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{on:{click:function(e){t.modifyDialogVisible=!1}}},[t._v("取 消")]),e("el-button",{attrs:{type:"primary"},on:{click:t.submitModifyUserInfo}},[t._v("提交")])],1)]),e("el-dialog",{attrs:{id:"pay-user",title:"支付","append-to-body":!0,visible:t.payDialogVisible,width:"30%"},on:{"update:visible":function(e){t.payDialogVisible=e}}},[e("div",{staticClass:"flex-center mb40"},[e("el-image",{staticStyle:{width:"200px"},attrs:{src:t.payImage}})],1),e("el-alert",{attrs:{center:"",title:"请打开小程序扫码充值",type:"success",closable:!1}})],1)],1),e("pay-meal-dialog",{ref:"paymeal",on:{onBuyMeal:t.onBuyMeal}}),e("pay-vip-dialog",{ref:"vipDialogRef",on:{onBuyVip:t.onBuyVip}})],1)},Rt=[];const Pt="https://ai-v2.deepcity.cn//default/head.png",Mt=d.A.create({baseURL:"https://ai-api.deepcity.cn/",timeout:5e3,headers:{"Content-Type":"application/json"},transformRequest:[function(t){return JSON.stringify(t)||{}}]});Mt.interceptors.response.use((t=>{const e=t;return e.data}),(t=>((0,h.Message)({message:t.msg?t.msg:"请求失败，请刷新重试",type:"error",duration:5e3}),Promise.reject(t))));function Lt(t){return S.post("user/api.user/smsLogin",t)}function St(t){return S.post("user/api.user/smsCode",t)}function Dt(t){return S.post("/square/api.chat/lunciHelpInfo",t)}function Tt(t){return S.post("/square/api.chat/joinHelpLunci",t)}function Et(){return S.post("/square/api.chat/helpLunciList")}async function Bt(){let t=C();if(!t)return!1;{let t=await S.post("user/api.userinfo/index");if(0==t.code){let e=t.data;return e.isLogin&&b(e),t}}}async function Ot({headImgUrl:t,nickname:e}){let i=await S.post("user/api.userinfo/update",{headImgUrl:t,nickname:e});return!!i}async function Ut(t){let{data:e}=await S.post("user/api.user/xiaoyiLoginCode",t);return e}const Vt=t=>S.post("/user/api.user/xiaoyiLoginCheck?msgId=msgId",{loginCode:t}),Gt=t=>S.post("/user/api.member/cardGoods",t),Ft=t=>S.post("/user/api.member/buyCard",t),Nt=t=>S.post("/user/api.member/memberCardBuyQuery",t),qt=t=>S.post("/user/api.member/userVipInfo",t),zt=t=>S.post("/user/api.member/userBigVipInfo",t),jt=t=>S.post("/user/api.member/applyBigVip",t),Ht=t=>S.post("/user/api.member/bigVipApplyList",t),Xt=t=>S.post("/user/api.member/withdrawAccountList",t),Qt=t=>S.post("/user/api.member/withdrawAccountAdd",t),Yt=t=>S.post("/user/api.member/withdrawAccountDelete",t),_t=t=>S.post("/user/api.member/withdrawApply",t),Jt=t=>S.post("/user/api.member/searchUser",t),Wt=t=>S.post("/user/api.member/giveAiPoint",t),Kt=t=>S.post("/user/api.member/sendAiPointRecord",t),Zt=t=>S.post("/user/api.member/withdrawApplyList",t),$t=t=>S.post("/user/api.member/bigVipInviteList",t),te=t=>S.post("/user/api.member/getBigVipConfig",t),ee=t=>S.post("/user/api.member/exchangeVipPoint",t),ie=t=>S.post("/user/api.user/aiMouseLogin",t),se=()=>S.post("/user/api.userinfo/helpUserInfo"),ae=t=>S.post("/user/api.userinfo/joinHelpUsers",t),oe=t=>S.post("/user/api.userinfo/helpUserOverview",t),ne=t=>S.post("/merchant/api.index/pcShowConfig",t),le=t=>S.post("/user/api.user/xiaoyiPcUrlGetMerchantGuid",t);var ce=function(){var t=this,e=t._self._c;return e("el-dialog",{staticClass:"pay-meal-dialog",attrs:{"before-close":t.handleClose,visible:t.visible,width:"900px"}},[e("div",{staticClass:"dialog-box"},[e("div",{staticClass:"title-box"},[e("div",{staticClass:"person"},[e("img",{staticClass:"icon",attrs:{src:i(8056)}})]),e("div",{staticClass:"title"},[t._v("购买套餐")]),e("div",{staticClass:"close",on:{click:t.handleClose}},[e("img",{staticClass:"icon",attrs:{src:i(5365)}})])]),e("div",{staticClass:"content"},t._l(t.items,(function(s,a){return e("div",{key:a,staticClass:"item"},[0==a?e("div",{staticClass:"title"},[e("img",{staticClass:"icon",attrs:{src:i(2602)}}),t._v("基础版 ")]):t._e(),1==a?e("div",{staticClass:"title"},[e("img",{staticClass:"icon",attrs:{src:i(5361)}}),t._v("进阶版 ")]):t._e(),2==a?e("div",{staticClass:"title"},[e("img",{staticClass:"icon",attrs:{src:i(901)}}),t._v("专业版 ")]):t._e(),e("div",{staticClass:"price"},[e("span",{staticClass:"unit"},[t._v("¥")]),t._v(t._s(s.price))]),e("div",{staticClass:"label"},[t._v("聊天点数："+t._s(s.chatCount))]),e("div",{staticClass:"label"},[t._v("有效天数：365天")]),e("div",{staticClass:"btn",on:{click:function(e){return t.pay(s.guid)}}},[t._v("立即购买")])])})),0)]),e("div",{ref:"payForm",domProps:{innerHTML:t._s(t.payForm)}})])},re=[],de=(i(4979),{name:"pay-meal-dialog",data(){return{visible:!1,items:[],payForm:""}},async mounted(){this.items=await Q()},methods:{async pay(t){let{payForm:e,orderNo:i}=await Y(t);e&&(this.payForm=window.atob(e),this.$nextTick((()=>{const t=this.$refs.payForm.querySelector("form");t&&(t.target="_blank",t.submit())})))},async bigVipPay(t){let e=await _({chatGoodsGuid:t,payEnv:"big_vip_balance"});0===e.code?(this.$message({message:"购买成功",type:"success"}),this.$emit("onBuyMeal"),this.visible=!1):this.$message({message:e.msg,type:"info"})},handleClose(){this.visible=!1},show(){this.visible=!0},close(){this.visible=!1}}}),he=de,ue=(0,wt.A)(he,ce,re,!1,null,"17b0b8f0",null),pe=ue.exports,ge=function(){var t=this,e=t._self._c;return e("el-dialog",{attrs:{"before-close":t.onCloseDialog,visible:t.visible,width:"812px"}},[e("div",{staticClass:"vip-dialog-box"},[e("div",{staticClass:"vip-list-box"},t._l(t.vipList,(function(s){return e("div",{key:s.guid,staticClass:"item",class:{active:t.activeItem?.sysId==s.sysId},on:{click:function(e){return t.onSelect(s)}}},[e("div",{staticClass:"title"},[t._v(t._s(s.cardName))]),e("img",{staticClass:"icon",attrs:{src:i(3772)}}),e("div",{staticClass:"content",style:{backgroundImage:`url(${s.bgImg})`}},[e("div",{staticClass:"price-box"},[e("div",{staticClass:"unit"},[t._v("¥")]),e("div",{staticClass:"price"},[t._v(t._s(s.cardPrice))]),e("div",{staticClass:"label"},[t._v("(仅"+t._s(s.averagePrice)+"元/天)")])]),e("div",{staticClass:"info-box"},[e("div",{staticClass:"li",domProps:{innerHTML:t._s(s.cardNotice)}})]),e("div",{staticClass:"button"},[t._v("权益："+t._s(s.cardUseDays)+"天")])])])})),0),e("div",{staticClass:"introduce-box"},[e("div",{staticClass:"text-box"},[t._v(" 购买须知： "),e("div",{domProps:{innerHTML:t._s(t.activeItem.cardDesc)}})]),e("div",{staticClass:"buy-box"},[e("div",{staticClass:"price-box"},[e("div",{staticClass:"unit"},[t._v("¥")]),e("div",{staticClass:"price"},[t._v(t._s(t.activeItem.cardPrice))]),e("div",{staticClass:"label"},[t._v("(仅"+t._s(t.activeItem.averagePrice)+"元/天)")])]),e("el-button",{staticClass:"button",attrs:{loading:t.buyStatus},on:{click:function(e){return t.onBuyVip(t.activeItem.guid)}}},[t._v("开通权益")])],1)])]),e("div",{ref:"payForm",domProps:{innerHTML:t._s(t.payForm)}})])},me=[],ve={name:"pay-vip-dialog",data(){return{visible:!1,vipList:[],activeItem:{guid:"",cardPrice:"",averagePrice:0,sysId:0},payForm:"",buyStatus:!1}},methods:{onCloseDialog(){this.visible=!1},show(){this.visible=!0},async getCardGoods(){let t=await Gt({merchantGuid:this.$store.state.merchantGuid});this.vipList=t.data,this.activeItem=t.data[0]},onSelect(t){this.activeItem=t},async onBuyVip(t){if(this.buyStatus)return;this.buyStatus=!0;let e=await Ft({merchantGuid:this.$store.state.merchantGuid,cardGuid:t,payType:"alipay_pc"});0===e.code?(this.interval(e.data.orderNo),e.data.payInfo.payForm&&(this.payForm=window.atob(e.data.payInfo.payForm),this.$nextTick((()=>{const t=this.$refs.payForm.querySelector("form");t&&(t.target="_blank",t.submit())})))):this.$message({message:e.msg,type:"info"})},interval(t){let e=0,i=0;e=setInterval((()=>{if(i>=12)return this.buyStatus=!1,void clearInterval(e);i++,this.getPayResult(t,e)}),3e3)},async getPayResult(t,e){let i=await Nt({orderNo:t});i.data.isPay&&(clearInterval(e),this.visible=!1,this.buyStatus=!1,this.$message({message:"购买成功,3秒后前往会员中心",type:"success"}),this.$emit("onBuyVip"),setTimeout((()=>{this.$router.push("/app/bigvip")}),3e3))}},mounted(){this.getCardGoods()}},Ce=ve,Ae=(0,wt.A)(Ce,ge,me,!1,null,"4de7de12",null),fe=Ae.exports,ye={name:"layout-header",components:{PayMealDialog:pe,PayVipDialog:fe},async mounted(){let t=localStorage.getItem("yqGuid");t&&this.showYQConfirm(t),this.getUserVipInfo(),this.getUserBigVipInfo(),this.getPcShowConfig(),this.getUserInfoFun()},data(){let t=C();return{modifyNickname:"",modifyHeaderImg:"",uploading:!1,session:{},token:t,headerImg:Pt,modifyDialogVisible:!1,payDialogVisible:!1,payImage:"",isVip:!1,isBigVip:!1,bigVipInfo:{cardInfo:{cardName:""},vipInfo:{cardEndTime:""}},invitationPath:"",pcConfig:{title:""}}},methods:{async getUserInfoFun(){let t=await Bt();if(this.session=y(),0==t.code){let e=window.location.origin;this.invitationPath=`${e}/?inviteCode=${t.data.guid}&type=user`}this.$store.commit("setUserGuid",t.data.guid)},async getPcShowConfig(){let{data:t}=await ne({merchantGuid:this.$store.state.merchantGuid});this.pcConfig=t,document.title=t.title?t.title:"AI龙龙，一个人的工作站"},copyPath(){this.$copyText(this.invitationPath),this.$message({message:"已复制分享链接",type:"success"})},async getUserBigVipInfo(){let t=await zt();this.isBigVip=t.data.isBigVip},async getUserVipInfo(){let t=await qt();this.bigVipInfo=t.data,this.isVip=t.data.isVip},onBuyVip(){this.getUserVipInfo()},async onBuyMeal(){await Bt(),this.session=y()},onGotoBigvip(){this.$router.push("/app/bigvip")},showPayMeal(){this.$refs.paymeal.show()},showVipDialog(){this.$refs.vipDialogRef.show()},async showYQConfirm(t){let e=await Dt({chatLunciGuid:t});if(0!==e.code)return this.$message({type:"info",message:e.msg||"获取协作聊天失败"}),void localStorage.setItem("yqGuid","");let i=`用户${e.data.lunciUser}邀请您为他协作${e.data.lunciName},是否接受。`;this.$confirm(i,"协作邀请",{confirmButtonText:"接收",cancelButtonText:"拒绝"}).then((async()=>{await Tt({chatLunciGuid:t}),localStorage.setItem("yqGuid",""),this.$router.push("/app/cooperation")})).catch((()=>{this.$message({type:"info",message:"拒绝邀请"}),localStorage.setItem("yqGuid","")}))},async submitModifyUserInfo(){this.modifyDialogVisible=!1;let t=await Ot({headImgUrl:this.modifyHeaderImg,nickname:this.modifyNickname});t&&this.$message({message:"修改成功",type:"success"})&&(await Bt(),this.session=y())},logout(){f(),this.$store.commit("clearMerchantGuid"),this.$router.push("/")},showModifyUser(){this.modifyDialogVisible=!0,this.modifyNickname=this.session.nickname,this.modifyHeaderImg=this.session.headImgUrl},handleAvatarSuccess({code:t,data:e}){0==t&&(this.modifyHeaderImg=e),this.uploading=!1},beforeAvatarUpload(){this.uploading=!0}}},be=ye,we=(0,wt.A)(be,kt,Rt,!1,null,null,null),Ie=we.exports,xe=function(){var t=this,e=t._self._c;return e("el-main",{staticClass:"layout-draw"},[e("el-container",[e("layout-draw-left",{attrs:{_status:t.status,_newOrderId:t.newOrderId,_tabActive:t.contentIndex},on:{showImages:t.setImages,changeStatus:t.changeStatus,updateConfig:t.updateConfig,changeContent:t.changeContent}}),e("layout-draw-main",{directives:[{name:"show",rawName:"v-show",value:"create"===t.contentIndex,expression:"contentIndex === 'create'"}],attrs:{_images:t.genImages,_status:t.status}}),"history"===t.contentIndex?e("layout-gallery-main",{on:{createImg:t.createNewImg}}):t._e()],1)],1)},ke=[],Re=function(){var t=this,e=t._self._c;return e("el-aside",{staticClass:"layout-draw-left",attrs:{width:"315px"}},[e("div",{staticClass:"tab-content-box"},[e("div",{class:["item",{active:"create"===t._tabActive}],on:{click:function(e){return t.onTabClick("create")}}},[t._v("创建绘画")]),e("div",{class:["item",{active:"history"===t._tabActive}],on:{click:function(e){return t.onTabClick("history")}}},[t._v("历史画板")])]),e("div",{staticClass:"title"},[t._v("图像描述")]),e("div",{staticClass:"pr14"},[e("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入你想生成的图像描述",resize:"none"},model:{value:t.userPrompt,callback:function(e){t.userPrompt=e},expression:"userPrompt"}})],1),e("div",{staticClass:"title mt20"},[t._v("参考图片 (可选)")]),e("div",[e("el-upload",{staticClass:"avatar-uploader",class:t.imageUrl?"hasImg":"",attrs:{"list-type":"picture-card",limit:1,name:"img",action:"https://ai-api.deepcity.cn/user/api.userinfo/uploadImg","on-remove":t.handleImageRemoved,"on-success":t.handleAvatarSuccess}},[t.imageUrl?t._e():e("i",{staticClass:"el-icon-plus avatar-uploader-icon"})])],1),e("div",{staticClass:"title mt20"},[t._v("模式选择 :")]),e("div",[e("el-select",{attrs:{placeholder:"请选择"},model:{value:t.drawMode,callback:function(e){t.drawMode=e},expression:"drawMode"}},t._l(t.drawLabels,(function(t){return e("el-option",{key:t.label,attrs:{label:t.label,value:t.label}})})),1)],1),e("div",{staticClass:"title mt20"},[t._v("绘画风格 :")]),e("div",{staticClass:"flex"},t._l(t.drawStyles,(function(i,s){return e("div",{key:s,staticClass:"draw-style",class:t.drawStyleIndex==s?"actived":"",on:{click:function(e){return t.chooseDrawStyle(s)}}},[e("el-image",{attrs:{src:i.image,fit:"fill"}}),e("div",{staticClass:"title-box"},[t._v(" "+t._s(i.title)+" ")])],1)})),0),e("div",{directives:[{name:"show",rawName:"v-show",value:"普通模式"==t.drawMode,expression:"drawMode == '普通模式'"}]},[e("div",{staticClass:"title mt20 cla"},[t._v("图片大小 :")]),e("div",{staticClass:"image-size flex"},t._l(t.imageSizes,(function(i,s){return e("div",{key:s,staticClass:"bili-item img-item",class:s==t.drawSizeIndex?"actived":"",on:{click:function(e){return t.chooseDrawSizeIndex(s)}}},[e("div",[e("div",{staticClass:"mt2"},[t._v(t._s(i))])])])})),0)]),e("div",{directives:[{name:"show",rawName:"v-show",value:"普通模式"==t.drawMode,expression:"drawMode == '普通模式'"}]},[e("div",{staticClass:"title mt20 cla"},[t._v("生成数量 :")]),e("div",{staticClass:"image-num flex"},t._l(t.imageNums,(function(i,s){return e("div",{key:s,staticClass:"bili-item",class:i==t.drawNum?"actived":"",on:{click:function(e){return t.chooseDrawNum(i)}}},[e("div",[e("span",[t._v(t._s(i)+" 张")])])])})),0)]),e("div",{directives:[{name:"show",rawName:"v-show",value:"专业模式"==t.drawMode,expression:"drawMode == '专业模式'"}]},[e("div",{staticClass:"title mt20 cla"},[t._v("比例 :")]),e("div",{staticClass:"image-bili flex flex-wrap"},[e("div",{staticClass:"flex"},t._l(t.imageBLs.slice(0,5),(function(i,s){return e("div",{key:s,staticClass:"bili-item",class:t.drawBiLiIndex==s?"actived":"",on:{click:function(e){return t.chooseDrawBili(s)}}},[e("div",{staticClass:"box",style:{width:i.w,height:i.h}}),e("div",[t._v(t._s(i.bl))])])})),0),e("div",{staticClass:"flex w-100 pr14"},[e("div",{staticClass:"bili-item",class:5==t.drawBiLiIndex?"actived":"",on:{click:function(e){return t.chooseDrawBili(5)}}},[e("div",{staticClass:"box",style:{width:"10px",height:"18px"}}),e("div",[t._v("16 : 9")])]),e("div",{staticClass:"custom-item",class:-1==t.drawBiLiIndex?"actived":"",on:{click:function(e){return t.chooseDrawBili(-1)}}},[e("div",{attrs:{clsss:"lable"}},[t._v("自定义")]),e("div",{staticClass:"inp-box"},[e("el-input",{model:{value:t.drawCustomBlX,callback:function(e){t.drawCustomBlX=e},expression:"drawCustomBlX"}}),t._v(" : "),e("el-input",{model:{value:t.drawCustomBlY,callback:function(e){t.drawCustomBlY=e},expression:"drawCustomBlY"}})],1)])])])]),e("div",{directives:[{name:"show",rawName:"v-show",value:"专业模式"==t.drawMode,expression:"drawMode == '专业模式'"}]},[e("div",{staticClass:"title mt20 cla flex space-between pr18"},[e("div",{on:{click:t.showProInfo}},[t._v(" 专业参数 : "),e("a",[e("i",{staticClass:"iconfont icon-info-circle"})])]),e("el-switch",{model:{value:t.isOpenProfessional,callback:function(e){t.isOpenProfessional=e},expression:"isOpenProfessional"}})],1),e("div",{directives:[{name:"show",rawName:"v-show",value:t.isOpenProfessional,expression:"isOpenProfessional"}],staticClass:"pr16"},[e("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入专业参数",resize:"none"},model:{value:t.professional,callback:function(e){t.professional=e},expression:"professional"}})],1)]),e("div",{staticClass:"mt20"},["0"==t._status||"2"==t._status?e("el-button",{staticClass:"startDraw",attrs:{size:"medium",type:"primary"},on:{click:t.startDraw}},[t._v("生成图片（ 消耗1个点算力 ） ")]):t._e(),"1"==t._status?e("el-button",{staticClass:"startDraw",attrs:{size:"medium",type:"primary"}},[t._v("正在生成图片...")]):t._e()],1),e("pro-examples-info",{ref:"proinfo"})],1)},Pe=[],Me=function(){var t=this,e=t._self._c;return e("el-dialog",{staticClass:"pro-examples-info",attrs:{title:"专业参数说明","before-close":t.handleClose,visible:t.visible,width:"40%"}},[e("el-input",{attrs:{type:"textarea",rows:20},model:{value:t.info,callback:function(e){t.info=e},expression:"info"}}),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{type:"primary"},on:{click:t.close}},[t._v("OK")])],1)],1)},Le=[],Se={name:"pro-examples-info",data(){return{visible:!1,info:"专业参数请选择原画风格，避免影响参数渲染效果。\n\n参数说明\n\n常用参数：\n\n--ar n:m 图片尺寸宽:高（Aspect Ratios），例如：--ar 16:9\n\n--chaos 0-100 变异程度，默认 0。数字越大，图片生成的想象力越大，例如：--chaos 50\n\n--iw 0-2 参考图权重，值越大，参考图的权重越大，默认 1。例如：--iw 1.25（仅在v5或者niji5模型下有效）\n\n--no 元素 排除某些元素，例如：--no plants，生成图中将不包含plants\n\n--q <.25、.5、1> 指定生成图的质量，默认 1。例如：--q .5（仅在v4、5，niji5下有效）\n\n--style raw 减少 midjourney 的艺术加工，生成更摄影化的图片。例如：--style raw（只在v5.1下有效）\n\n--style <cute, expressive, original, or scenic> 设置动漫风格：可爱、表现力、原始、或者风景。例如：--style cute（只在--niji 5下有效）\n\n--s（或--stylize） 数字 设置midjourney的艺术加工权重，默认 100。例如：--s 100。取值范围： 0-1000（v4、v5），626-60000（v3），niji模式下无效\n\n--niji 模型设置。设置为日本动漫风格模型，例如：--niji，也可以写成：--niji 5（目前 5 可以省略）\n\n--v <1-5>\t模型设置。设置模型版本，例如：--v 5\n"}},methods:{handleClose(){this.visible=!1},show(){this.visible=!0},close(){this.visible=!1}}},De=Se,Te=(0,wt.A)(De,Me,Le,!1,null,null,null),Ee=Te.exports;async function Be(){let{data:t}=await S.post("square/api.ContentGeneration/categoryContentList",{cateId:6});return t}async function Oe(t={}){let{data:e}=await S.post("user/api.userWork/exampleInfo",t);return e}async function Ue(t){let e=await D.post("user/api.userWork/createImgOrder",t);return e}async function Ve(t){let{data:e}=await S.post("user/api.userWork/imgOrderInfo",{orderNo:t});return{images:e.imageResult}}async function Ge(t=1,e=50,i="image"){let{data:s}=await S.post("user/api.userWork/workRecord",{page:t,pageSize:e,workType:i}),a=[];for(let{guid:o,workResult:n,workParams:l,orderNo:c,workStatus:r}of s.data)a.push({guid:o,img:n[0],title:l.title,orderNo:c,workStatus:r});return a}async function Fe(t){let{data:e}=await S.post("user/api.userWork/imgOrderInfo",{orderNo:t}),i=e.imgChangeInfo;if(i&&i!=[]){let t=i?.able_U?.components,e=i?.able_V?.components;return{ableU_items:t,ableV_items:e}}return null}async function Ne(t,e){let{data:i}=await D.post("user/api.userWork/createImgOrderChange",{oldImgOrderSn:t,custom_id:e});return i}async function qe(t,e){let{data:i}=await D.post("user/api.userWork/createImgOrderUpscale",{oldImgOrderSn:t,custom_id:e});return i}function ze(t){return S.post("/user/api.userWork/recordDel",t)}var je={name:"layout-draw-left",components:{ProExamplesInfo:Ee},async mounted(){this.drawStyles=await Be();let{imageExampleText:t,imageExampleUrl:e}=await Oe();this.exampleInfo.imageExampleText=t,this.exampleInfo.imageExampleUrl=e},destroyed(){this.intervalId&&clearInterval(this.intervalId)},props:{_status:String,_newOrderId:String,_tabActive:String},watch:{async _newOrderId(t){this.waitingDraw(t),this.generateOrderId=t}},data(){return{drawContentIndex:"create",exampleInfo:{},drawStyles:[],drawStyleIndex:0,drawBiLiIndex:0,drawNum:1,drawSizeIndex:0,drawCustomBlX:1,drawCustomBlY:1,imageUrl:"",userPrompt:"",drawMode:"专业模式",drawLabels:[{label:"专业模式"},{label:"普通模式"}],professional:"",isOpenProfessional:!1,imageSizes:["1024*1024","512*512","256*256"],imageNums:[1,2,4],imageBLs:[{name:"头像 ",bl:"1 : 1",w:"18px",h:"18px"},{name:"文章配图 ",bl:"3 : 2",w:"18px",h:"12px"},{name:"社交媒体",bl:"3 : 4",w:"14px",h:"18px"},{name:"公众号配图",bl:"4 : 3",w:"18px",h:"14px"},{name:"海报图",bl:"9 : 16",w:"10px",h:"18px"},{name:"电脑壁纸",bl:"16 : 9",w:"18px",h:"10px"}],generateOrderId:"",imageGenerating:!1,intervalId:null}},methods:{onTabClick(t){this.$emit("changeContent",t)},chooseDrawStyle(t){this.drawStyleIndex=t},chooseDrawBili(t){this.drawBiLiIndex=t},chooseDrawNum(t){this.drawNum=t},chooseDrawSizeIndex(t){this.drawSizeIndex=t},_getImageSize(){return"普通模式"==this.drawMode?this.imageSizes[this.drawSizeIndex]:this.drawBiLiIndex>-1?this.imageBLs[this.drawBiLiIndex].bl.replace(/\s/g,""):this.drawCustomBlX+":"+this.drawCustomBlY},async startDraw(){let t=this.drawStyles[this.drawStyleIndex].id,e={model:"普通模式"==this.drawMode?"replicateSdxl":"midjourney",copywritingCategoryId:t,userPrompt:this.userPrompt,imageNum:"普通模式"==this.drawMode?this.drawNum:1,imgUrl:this.imageUrl,imageSize:this._getImageSize(),isOpenProfessional:1==this.isOpenProfessional?1:0,professional:this.professional};try{let t=await Ue(e);0==t.code?(this.generateOrderId=t.data,this.$emit("updateConfig",e),this.waitingDraw(t.data)):this.$message({message:"生成错误",type:"info"})}catch(i){console.log(i,"errorerror")}},waitingDraw(t){this.$emit("changeStatus","1"),this.intervalId=setInterval((async()=>{let{images:e}=await Ve(t);e&&e.length>0&&(clearInterval(this.intervalId),this.$emit("showImages",e),this.$emit("changeStatus","2"))}),5e3)},showProInfo(){this.$refs.proinfo.show()},handleAvatarSuccess({code:t,data:e}){0==t&&(this.imageUrl=e)},handleImageRemoved(){this.imageUrl=""},beforeAvatarUpload(){}}},He=je,Xe=(0,wt.A)(He,Re,Pe,!1,null,null,null),Qe=Xe.exports,Ye=function(){var t=this,e=t._self._c;return e("div",{staticClass:"layout-draw-main"},["0"==t._status?e("div",{staticClass:"empty-panel"},[e("img",{staticClass:"loding-img",attrs:{src:i(6375)}}),e("div",{staticClass:"info mt20"},[t._v(t._s(t.pcConfig.title))])]):t._e(),"1"==t._status?e("div",{staticClass:"loading-panel ml30"},[t._m(0)]):t._e(),t._images.length>0&&"2"==t._status?e("div",{staticClass:"show-panel"},[t._l(t._images,(function(t,i){return e("el-image",{key:i,staticClass:"img",attrs:{src:t,"preview-src-list":[t]}},[e("div",{staticClass:"image-slot",attrs:{slot:"placeholder"},slot:"placeholder"},[e("i",{staticClass:"el-icon-loading"})])])})),e("div",{staticClass:"mt20 opt-panel"})],2):t._e()])},_e=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"loader"},[e("div",{staticClass:"dot"}),e("div",{staticClass:"dot"}),e("div",{staticClass:"dot"}),e("div",{staticClass:"dot"}),e("div",{staticClass:"dot"}),e("div",{staticClass:"mt60 mb20 loading-text"},[t._v("正在生成中，需要大约1-2分钟的时间，请耐心等候")])])}],Je={name:"layout-draw-main",props:{_images:Array,_status:String},data(){return{pcConfig:{title:""}}},mounted(){this.getPcShowConfig()},methods:{async getPcShowConfig(){let{data:t}=await ne({merchantGuid:this.$store.state.merchantGuid});this.pcConfig=t}}},We=Je,Ke=(0,wt.A)(We,Ye,_e,!1,null,null,null),Ze=Ke.exports,$e=function(){var t=this,e=t._self._c;return e("div",{staticClass:"layout-gallery-main pd10"},[e("div",{directives:[{name:"infinite-scroll",rawName:"v-infinite-scroll",value:t.load,expression:"load"}],staticClass:"flex",staticStyle:{"flex-wrap":"wrap"}},t._l(t.records,(function(i){return e("el-card",{key:i.guid,staticClass:"draw-style pointer"},[e("div",{staticClass:"delete-box",on:{click:function(e){return t.onDelete(i)}}},[e("i",{staticClass:"el-icon-delete",attrs:{color:"#FFFFFF"}})]),"success"==i.workStatus?e("el-image",{attrs:{src:i.img,fit:"cover"},nativeOn:{click:function(e){return t.showDialogImage(i)}}},[e("div",{staticClass:"image-slot",attrs:{slot:"placeholder"},slot:"placeholder"},[e("i",{staticClass:"el-icon-loading"})])]):t._e(),"doing"==i.workStatus||"wait"==i.workStatus?e("div",{staticClass:"image-slot",staticStyle:{color:"var(--primary)"}},[e("img",{attrs:{src:"/imgs/status/doing.png",alt:""}})]):t._e(),"fail"==i.workStatus?e("div",{staticClass:"image-slot"},[e("img",{attrs:{src:"/imgs/status/fail.png",alt:""}})]):t._e(),e("div",{staticClass:"title-bg"},[t._v(" "+t._s(i.title)+" ")])],1)})),1),e("el-dialog",{staticClass:"img-dialog",attrs:{visible:t.dialogVisible,fullscreen:!0},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("div",{staticClass:"flex-column"},[e("el-image",{staticClass:"center",attrs:{src:t.dialogImage,fit:"contain"}}),e("div",{staticClass:"center mt10"},[t._v(" "+t._s(t.dialogTitle)+" ")]),e("div",{staticClass:"center mt10"},[e("el-link",{attrs:{type:"success"},on:{click:function(e){return t.openLink(t.dialogImage)}}},[t._v("浏览器打开")])],1),t.imgOpts?e("div",{staticClass:"btn-opt mt20 mb30"},[t.imgOpts.ableU_items?e("el-row",t._l(t.imgOpts.ableU_items,(function(i,s){return e("el-button",{key:s,attrs:{disabled:1==i.is_use,type:"primary",size:"mini",plain:""},on:{click:function(e){return t.createImage(i)}}},[t._v("放大 "+t._s(i.label)+" ")])})),1):t._e(),t.imgOpts.ableV_items?e("el-row",{staticClass:"mt10"},t._l(t.imgOpts.ableV_items,(function(i,s){return e("el-button",{key:s,attrs:{disabled:1==i.is_use,type:"primary",size:"mini",plain:""},on:{click:function(e){return t.createImage(i,"change")}}},[t._v("变化 "+t._s(i.label)+" ")])})),1):t._e()],1):t._e()],1),e("div",{staticClass:"mt20"},[e("el-button",{staticStyle:{width:"30%"},attrs:{round:"",type:"success"},on:{click:function(e){t.dialogVisible=!1}}},[t._v("关闭")])],1)])],1)},ti=[],ei={name:"layout-gallery-main",async mounted(){console.log("records : ",this.records)},data(){return{records:[],currPage:0,currentDate:new Date,dialogVisible:!1,dialogImage:"",dialogTitle:"",imgOpts:"",dialogOrderId:""}},methods:{async onDelete(t){await ze({recordGuid:t.guid});let e=await Ge(1);this.records=e,this.$message({message:"删除成功",type:"success"})},async load(){let t=this.currPage+1,e=await Ge(t);e.length>0&&(this.records.push(...e),this.currPage++)},async createImage({custom_id:t},e){this.$confirm("生成图片将消耗1个算力, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{let i;i="change"==e?await Ne(this.dialogOrderId,t):await qe(this.dialogOrderId,t),i?(this.$emit("createImg",i),this.dialogVisible=!1):alert("出现错误，orderId为空")}))},openLink(t){window.open(t,"_blank")},async showDialogImage({orderNo:t,img:e,title:i}){this.imgOpts=await Fe(t),this.dialogOrderId=t,this.dialogImage=e,this.dialogTitle=i,this.dialogVisible=!0}}},ii=ei,si=(0,wt.A)(ii,$e,ti,!1,null,null,null),ai=si.exports,oi={name:"layout-draw",components:{LayoutDrawMain:Ze,LayoutDrawLeft:Qe,LayoutGalleryMain:ai},async mounted(){},data(){return{genImages:["https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/55d3039bb20b4830bc11a5a1e8701cce.png"],status:"0",config:{},contentIndex:"create",newOrderId:""}},methods:{setImages(t){this.genImages=t},changeStatus(t){this.status=t},updateConfig(t){this.config=t},changeContent(t){this.contentIndex=t},createNewImg(t){this.newOrderId=t,this.contentIndex="create"}}},ni=oi,li=(0,wt.A)(ni,xe,ke,!1,null,null,null),ci=li.exports,ri=function(){var t=this,e=t._self._c;return e("el-main",{staticClass:"layout-chat"},[e("el-container",[e("layout-chat-left",{attrs:{_chatList:t.chatList,_chatingId:t.chatingId,_onChatState:t.onChatState},on:{reloadList:t.loadChatList,"update:_chatingId":function(e){t.chatingId=e},"update:_chating-id":function(e){t.chatingId=e},createChat:t.createChat}}),e("layout-chat-main",{attrs:{_chatObj:t.chatObj,_chatDialogs:t.chatDialogs,_chatingId:t.chatingId},on:{clearChatDialogs:t.clearChatDialogs,changeOnChatState:t.changeOnChatState}})],1)],1)},di=[],hi=function(){var t=this,e=t._self._c;return e("div",{staticClass:"layout-chat-left"},[e("div",{staticClass:"search-panel"},[e("el-input",{attrs:{size:"large",placeholder:"搜索","prefix-icon":"el-icon-search"},on:{input:t.filter},model:{value:t.searchWord,callback:function(e){t.searchWord=e},expression:"searchWord"}})],1),e("div",{staticClass:"chats-panel pt10"},t._l(t._chatList,(function(i,s){return e("div",{directives:[{name:"show",rawName:"v-show",value:i.visible,expression:"item.visible"}],key:s,staticClass:"chat-item",class:t._chatingId==i.guid?"active":""},[e("div",{staticClass:"chat-name-box"},[e("div",{staticClass:"name"},[e("span",{on:{click:function(e){return t.chooseChat(i)}}},[t._v(t._s(i.chatLunciTitle))]),e("a",{staticClass:"ml10",on:{click:t.modifyDialog}},[e("i",{staticClass:"el-icon-edit"})])]),e("div",{staticClass:"date",on:{click:function(e){return t.chooseChat(i)}}},[t._v(t._s(i.showTime))])]),e("div",{staticClass:"chat-icon-box"},[e("div",{staticClass:"opts"},[e("el-popconfirm",{attrs:{title:"确定删除吗 ？"},on:{confirm:function(e){return t.removeChatDialog(i.guid)}}},[e("a",{attrs:{slot:"reference"},slot:"reference"},[e("i",{staticClass:"el-icon-delete"})])])],1)])])})),0),e("div",{staticClass:"btn-box"},[e("div",{staticClass:"add-chat-btn",on:{click:t.createChat}},[e("img",{staticClass:"icon",attrs:{src:i(9359)}}),t._v(" 新增对话 ")])])])},ui=[],pi={name:"layout-chat-left",async mounted(){},props:{_chatingId:String,_chatList:Array,_onChatState:Boolean},data(){return{searchWord:""}},methods:{filter(){""===this.searchWord?this._chatList.forEach((t=>{t.visible=!0})):this._chatList.forEach((t=>{t.visible=t.chatLunciTitle.includes(this.searchWord)}))},async removeChatDialog(t){await F(t),this.$emit("reloadList"),this.$message({message:"已删除",type:"success"})},modifyDialog(){let t=this,e=this._chatList.find((t=>t.guid==this._chatingId)),i=this._chatList.findIndex((e=>e.guid==t._chatingId));this.$prompt("","修改对话",{inputValue:e.chatLunciTitle,confirmButtonText:"确定",cancelButtonText:"取消"}).then((async({value:s})=>{await V(e.guid,s),this.$set(this._chatList[i],"chatLunciTitle",s),t.$message({showClose:!0,message:"更新成功",type:"success"})}))},createChat(){this.$emit("createChat")},chooseChat({guid:t}){this._onChatState?this.$emit("update:_chatingId",t):this.$message({showClose:!0,message:"有生成任务进行中，暂不可切换",type:"warning"})}},computed:{}},gi=pi,mi=(0,wt.A)(gi,hi,ui,!1,null,"22a2bba2",null),vi=mi.exports,Ci=function(){var t=this,e=t._self._c;return e("div",{staticClass:"container"},[e("div",{staticClass:"layout-chat-main"},[t._chatObj?e("div",{staticClass:"chat-header"},[e("div",{staticClass:"left-box"},[t.xzConfig.isHelp?t._e():e("div",{staticClass:"ml10 invitation-btn top-public-btn",on:{click:t.onInvitation}},[e("i",{staticClass:"iconfont el-icon-share mr4"}),t._v(" 邀请协助 ")]),t.xzConfig.isHelp?e("div",{staticClass:"ml10 cooperation-btn top-public-btn"},[e("img",{staticClass:"icon",attrs:{src:i(2418)}}),t._v(" 协助中 ")]):t._e(),t.xzConfig.isHelp?t._e():e("div",{staticClass:"ml10 cooperation-pop-btn top-public-btn",on:{click:function(e){t.reqXzUserListConfig.isShowPop=!0}}},[t._v(" 协作联盟 ")])]),t.xzConfig.isHelp?e("div",{staticClass:"center-box"},[e("el-popconfirm",{attrs:{title:"确认结束协助，是否继续？"},on:{confirm:function(e){return t.onChangeXzStatus("end")}}},[e("div",{staticClass:"end-btn top-public-btn ml10",attrs:{slot:"reference"},slot:"reference"},[e("img",{staticClass:"icon",attrs:{src:i(2473)}}),t._v("结束 ")])]),e("el-popconfirm",{attrs:{title:"确认返回协助，，是否继续？"},on:{confirm:function(e){return t.onChangeXzStatus("return")}}},[e("div",{staticClass:"back-btn top-public-btn ml10",class:{active:!t.xzConfig.helpInfo.isAbleReturn},attrs:{slot:"reference"},slot:"reference"},[t.xzConfig.helpInfo.isAbleReturn?e("img",{staticClass:"icon",attrs:{src:i(5681)}}):e("img",{staticClass:"icon",attrs:{src:i(4083)}}),t._v(" 返回 ")])])],1):t._e(),e("div",{staticClass:"right-box"},[t.xzConfig.isHelp?e("el-dropdown",{staticClass:"user-dropdown",attrs:{trigger:"hover"}},[e("div",{staticClass:"xz-logo-box"},[e("img",{staticClass:"img",attrs:{src:t.xzConfig.helpInfo.helpUser.avatarUrl}})]),e("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[e("div",{staticClass:"xz-user-dropdown-menu"},[e("div",{staticClass:"logo-box"},[e("div",{staticClass:"logo"},[e("img",{staticClass:"img",attrs:{src:t.xzConfig.helpInfo.helpUser.avatarUrl}})]),e("div",{staticClass:"name-box"},[e("div",{staticClass:"name"},[t._v(t._s(t.xzConfig.helpInfo.helpUser.nickname))]),e("div",{staticClass:"label"},[t._v(t._s(t.xzConfig.helpInfo.helpUser.introduction))])])]),e("div",{staticClass:"count"},[t._v("已协助次数："+t._s(t.xzConfig.helpInfo.debugChatCount))])])])],1):t._e()],1)]):t._e(),e("chat-box",{ref:"chatBox",attrs:{id:"chatbox",_chatDialogs:t._chatDialogs},on:{onPause:t.onPause}}),e("div",{staticClass:"chat-input"},[e("div",{staticClass:"opt-panel"},[e("div",{ref:"aiPopRef",staticClass:"popper-box mr4"},[t.modelPopCofig.aiPop?e("div",{staticClass:"dropdown-box"},t._l(t.aiModels,(function(i){return e("div",{key:i.sign,class:["item",{active:t.modelPopCofig.modelVendorSign==i.sign}],on:{click:function(e){return t.onModelChange(i)}}},[t._v(" "+t._s(i.name)+" "),e("i",{staticClass:"el-icon-arrow-right el-icon--right ml-10"})])})),0):t._e(),t.modelPopCofig.aiPop?e("div",{staticClass:"children-dropdown-box"},[e("div",{staticClass:"tab"},[e("div",{staticClass:"item",class:{active:"text"===t.modelPopCofig.modelType},on:{click:function(e){return t.onModelTypeChange("text")}}},[t._v(" 文字 ")]),e("div",{staticClass:"item",class:{active:"image"===t.modelPopCofig.modelType},on:{click:function(e){return t.onModelTypeChange("image")}}},[t._v(" 图片 ")]),e("div",{staticClass:"item",class:{active:"video"===t.modelPopCofig.modelType},on:{click:function(e){return t.onModelTypeChange("video")}}},[t._v(" 视频 ")])]),e("div",{staticClass:"content"},[t._l(t.aiModelChildren,(function(i){return e("div",{staticClass:"item",class:{active:t.modelPopCofig.guid===i.guid},on:{click:function(e){return t.onModelChoose(i)}}},[e("div",{staticClass:"name"},[t._v(t._s(i.modelName))]),e("div",{staticClass:"price"},[t._v("价格："+t._s(0==i.usePrice?"免费":i.usePrice))]),e("el-tooltip",{attrs:{effect:"light",content:i.modelDesc,placement:"top-start"}},[e("div",{staticClass:"specific"},[t._v("特点："+t._s(i.modelDesc))])])],1)})),0===t.aiModelChildren.length||t.modelPopCofig.isTabLoading?e("div",{staticClass:"empty"},[t.modelPopCofig.isTabLoading?e("p",[t._v("加载中...")]):e("p",[t._v("暂无可用模型")])]):t._e()],2)]):t._e(),e("div",{class:["model-public-btn",{active:t.modelPopCofig.aiPop}],on:{click:t.onModelBtn}},[t._v(" 选择AI模型："+t._s(t.modelPopCofig.modelName)+" ")])]),e("el-dropdown",{staticClass:"mr4",attrs:{trigger:"click"}},[e("div",{staticClass:"role-select-btn"},[t._v(" 选择AI角色"),"base"===t.selectRole.type?e("span",[t._v("："+t._s(t.selectRole.name))]):t._e()]),e("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},t._l(t.baseRoles,(function(i){return e("el-dropdown-item",{key:i.id},[e("div",{staticClass:"flex role-drop-box"},[e("div",{staticClass:"role-drop-title mr-4",on:{click:function(e){return t.choooseRole(i,"base")}}},[t._v(" "+t._s(i.name)+" ")])])])})),1)],1),e("el-dropdown",{staticClass:"mr4",attrs:{trigger:"click"}},[e("div",{staticClass:"role-select-btn",on:{click:t.onXzRole}},[t._v(" 选择协助角色"),"xz"===t.selectRole.type?e("span",[t._v("："+t._s(t.selectRole.name))]):t._e()]),e("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},t._l(t.roles,(function(i){return e("el-dropdown-item",{key:i.id},[e("div",{staticClass:"flex role-drop-box"},[e("div",{staticClass:"role-drop-title mr-4",on:{click:function(e){return t.choooseRole(i,"xz")}}},[t._v(" "+t._s(i.name)+" ")])])])})),1)],1),e("div",{staticClass:"clean-chat-btn mr4",on:{click:t.cleanDialogs}},[t._v("清除聊天记录")]),0==t.msgImgsCount&&1==t.modelDetail.isImageRecognition?e("div",{staticClass:"clean-chat-btn mr4",attrs:{disabled:0==t.modelDetail.isImageRecognition},on:{click:function(e){return t.onShowPop("img")}}},[e("i",{staticClass:"el-icon-upload2"}),t._v(" 上传图像 ")]):t._e(),t.msgImgsCount>0?e("el-badge",{staticClass:"ml4",attrs:{value:t.msgImgsCount}},[e("div",{staticClass:"clean-chat-btn mr4",attrs:{disabled:0==t.modelDetail.isImageRecognition},on:{click:function(e){return t.onShowPop("img")}}},[e("i",{staticClass:"el-icon-upload2"}),t._v(" 上传图像 ")])]):t._e(),0==t.msgVideoCount&&1==t.modelDetail.isVideoRecognition?e("div",{staticClass:"clean-chat-btn mr4",attrs:{disabled:0==t.modelDetail.isVideoRecognition},on:{click:function(e){return t.onShowPop("video")}}},[e("i",{staticClass:"el-icon-upload2"}),t._v(" 上传视频 ")]):t._e(),t.msgVideoCount>0?e("el-badge",{staticClass:"ml4",attrs:{value:t.msgVideoCount}},[e("div",{staticClass:"clean-chat-btn mr4",attrs:{disabled:0==t.modelDetail.isVideoRecognition},on:{click:function(e){return t.onShowPop("video")}}},[e("i",{staticClass:"el-icon-upload2"}),t._v(" 上传视频 ")])]):t._e()],1),e("div",{staticClass:"chat-input-box"},[e("div",{staticClass:"input-box"},[e("el-input",{attrs:{type:"textarea",autosize:{minRows:2,maxRows:12},placeholder:t.reqAiConfig.placeholder,resize:"none"},model:{value:t.textarea,callback:function(e){t.textarea=e},expression:"textarea"}})],1),e("div",{staticClass:"send-icon"},[e("img",{staticClass:"icon",attrs:{src:i(8681)},on:{click:t.send}})])])]),e("el-dialog",{attrs:{title:"图像上传",visible:t.reqXzUserListConfig.isShowPop,width:"1000px"},on:{"update:visible":function(e){return t.$set(t.reqXzUserListConfig,"isShowPop",e)}}},[e("div",{staticClass:"xz-users-dialog"},[e("div",{staticClass:"content"},t._l(t.reqXzUserListConfig.list,(function(i){return e("div",{key:i.guid,staticClass:"item"},[e("div",{staticClass:"top-box"},[e("div",{staticClass:"logo"},[e("img",{staticClass:"img",attrs:{src:i.avatarUrl}})]),e("div",{staticClass:"name-box"},[e("p",{staticClass:"name"},[t._v(t._s(i.nickname))]),e("div",{staticClass:"yq-btn",on:{click:function(e){return t.onInvite(i.guid)}}},[t._v("请他协助我")])])]),e("div",{staticClass:"lable"},[t._v(" "+t._s(i.introduction)+" ")])])})),0),e("el-pagination",{staticClass:"mt20",attrs:{background:"",layout:"prev, pager, next",total:t.reqXzUserListConfig.total,"current-page":t.reqXzUserListConfig.page},on:{"current-change":t.handlePageChang}})],1)]),e("el-dialog",{attrs:{title:"图像上传",visible:t.imgInputVisible,width:"500px"},on:{"update:visible":function(e){t.imgInputVisible=e}}},[e("div",{staticClass:"center"},[e("el-upload",{ref:"imgRef",attrs:{name:"img","list-type":"picture",limit:3,"on-success":t.uploadSuccess,"before-remove":t.beforeRemove,"on-exceed":t.handleExceed,drag:"",action:"https://ai-api.deepcity.cn/user/api.userinfo/uploadImg"}},[e("i",{staticClass:"el-icon-upload"}),e("div",{staticClass:"el-upload__text"},[t._v("将文件拖到此处，或"),e("em",[t._v("点击上传")])]),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("只能上传jpg/png文件，且不超过5mb")])])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{type:"primary"},on:{click:function(e){t.imgInputVisible=!1}}},[t._v("确 定")])],1)]),e("el-dialog",{attrs:{title:"视频上传",visible:t.videoInputVisible,width:"500px"},on:{"update:visible":function(e){t.videoInputVisible=e}}},[e("div",{staticClass:"center"},[e("el-upload",{ref:"videoRef",attrs:{name:"video","list-type":"text",limit:1,"on-success":t.videoUploadSuccess,"before-remove":t.videoBeforeRemove,accept:".mp4","on-exceed":t.handleExceed,drag:"",action:"https://ai-api.deepcity.cn/user/api.userinfo/uploadVideo"}},[e("i",{staticClass:"el-icon-upload"}),e("div",{staticClass:"el-upload__text"},[t._v("将文件拖到此处，或"),e("em",[t._v("点击上传")])]),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("请上传MP4文件并且大小不超过100M的视频文件")])])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{type:"primary"},on:{click:function(e){t.videoInputVisible=!1}}},[t._v("确 定")])],1)]),e("el-dialog",{attrs:{title:"预览",visible:t.isPrviewShow,width:"800px"},on:{"update:visible":function(e){t.isPrviewShow=e}}},[e("el-input",{attrs:{type:"textarea",rows:20,placeholder:"请输入参数（输入后请点击保存）"},model:{value:t.systemPrompt,callback:function(e){t.systemPrompt=e},expression:"systemPrompt"}}),e("div",{staticClass:"prompt-btn-box"},[e("el-button",{attrs:{type:"primary"},on:{click:t.onSavePrompt}},[t._v("保 存")]),e("el-button",{attrs:{type:"info"},on:{click:function(e){t.isPrviewShow=!1}}},[t._v("取 消")])],1)],1)],1),e("div",{staticClass:"options-box"},[e("div",{staticClass:"param-box"},[e("div",{staticClass:"label"},[t._v("参数设置")]),e("div",{staticClass:"title mt10"},[t._v("system_prompt")]),e("div",{staticClass:"input-box mt10"},[e("el-input",{staticClass:"inp",attrs:{type:"textarea",rows:2,placeholder:"请输入参数",resize:"none"},model:{value:t.reqAiConfig.systemPrompt,callback:function(e){t.$set(t.reqAiConfig,"systemPrompt",e)},expression:"reqAiConfig.systemPrompt"}}),e("el-button",{staticClass:"el-dropdown-link",staticStyle:{padding:"2px 6px","margin-bottom":"0"},attrs:{size:"mini",round:""}},[e("i",{staticClass:"el-icon-full-screen"})]),e("div",{staticClass:"zw-box",on:{click:t.onOpenSavePop}})],1)]),e("div",{staticClass:"slider-box"},[e("div",{staticClass:"item"},[e("div",{staticClass:"title"},[t._v(" max_tokens "),e("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"最大输出token数",placement:"top-start"}},[e("i",{staticClass:"el-icon-warning-outline"})])],1),e("div",{staticClass:"slider"},[e("div",{staticClass:"label"},[t._v(t._s(t.reqAiConfig.maxOutputTokens))]),e("el-slider",{attrs:{max:t.modelDetail.maxOutputTokens,step:1},model:{value:t.reqAiConfig.maxOutputTokens,callback:function(e){t.$set(t.reqAiConfig,"maxOutputTokens",e)},expression:"reqAiConfig.maxOutputTokens"}})],1)]),e("div",{staticClass:"item"},[e("div",{staticClass:"title"},[t._v(" temperature "),e("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"采样温度，控制输出的随机性，取值范围是：[0.0, 1.0]",placement:"top-start"}},[e("i",{staticClass:"el-icon-warning-outline"})])],1),e("div",{staticClass:"slider"},[e("div",{staticClass:"label"},[t._v(t._s(t.reqAiConfig.temperature))]),e("el-slider",{attrs:{step:.1,min:0,max:1},model:{value:t.reqAiConfig.temperature,callback:function(e){t.$set(t.reqAiConfig,"temperature",e)},expression:"reqAiConfig.temperature"}})],1)]),e("div",{staticClass:"item"},[e("div",{staticClass:"title"},[t._v(" top_p "),e("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"温度取样的另一种方法，取值范围是：[0.0, 1.0]",placement:"top-start"}},[e("i",{staticClass:"el-icon-warning-outline"})])],1),e("div",{staticClass:"slider"},[e("div",{staticClass:"label"},[t._v(t._s(t.reqAiConfig.topP))]),e("el-slider",{attrs:{step:.1,min:0,max:1},model:{value:t.reqAiConfig.topP,callback:function(e){t.$set(t.reqAiConfig,"topP",e)},expression:"reqAiConfig.topP"}})],1)])]),e("div",{staticClass:"switch-box"},[e("div",{staticClass:"label"},[t._v("工具调用")]),e("div",{staticClass:"item"},[e("div",{staticClass:"t"},[t._v("网页检索")]),e("div",{staticClass:"switch"},[e("el-switch",{attrs:{activeValue:1,inactiveValue:0,"active-color":"#6126F5","inactive-color":"#DEE0E4",disabled:""},model:{value:t.modelDetail.isNetwork,callback:function(e){t.$set(t.modelDetail,"isNetwork",e)},expression:"modelDetail.isNetwork"}})],1)])]),e("div",{staticClass:"switch-box"},[e("div",{staticClass:"item"},[e("div",{staticClass:"t"},[t._v("多轮对话")]),e("div",{staticClass:"switch"},[e("el-switch",{attrs:{"active-color":"#6126F5","inactive-color":"#DEE0E4"},model:{value:t.reqAiConfig.isMultiple,callback:function(e){t.$set(t.reqAiConfig,"isMultiple",e)},expression:"reqAiConfig.isMultiple"}})],1)])])])])},Ai=[],fi=function(){var t=this,e=t._self._c;return e("div",{staticClass:"chat-box"},t._l(t._chatDialogs,(function(i,s){return e("div",{key:s,staticClass:"chat-item",class:"assistant"!=i.chatRole?"mine":""},["assistant"!=i.chatRole?e("div",{staticClass:"header-img ml10"},[t.session?e("img",{attrs:{src:t.session.headImgUrl,alt:""}}):t._e()]):t._e(),"assistant"==i.chatRole?e("div",{staticClass:"header-img ml10"},[e("img",{attrs:{src:"https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/76ab72d19e2249a28f77e2d488de8c3f.jpg",alt:""}})]):t._e(),e("div",{staticClass:"content"},[e("div",{directives:[{name:"show",rawName:"v-show",value:i.starting,expression:"item.starting"}],staticClass:"loading"},[t._v("思考中 ...")]),e("v-md-preview",{directives:[{name:"show",rawName:"v-show",value:!i.starting,expression:"!item.starting"}],staticClass:"preview-box",attrs:{text:i.chatContent}}),t._l(i.imgUrls,(function(t,s){return e("el-image",{key:s,attrs:{src:t,"preview-src-list":i.imgUrls}})})),i.videoUrls&&i.videoUrls.length>0?e("video",{attrs:{width:"220",height:"160",controls:""}},[e("source",{attrs:{src:i.videoUrls[0],type:"video/mp4"}}),e("source",{attrs:{src:i.videoUrls[0],type:"video/ogg"}}),t._v(" 您的浏览器不支持 HTML5 video 标签。 ")]):t._e(),"assistant"==i.chatRole?e("div",{directives:[{name:"show",rawName:"v-show",value:!i.starting&&!i.finished,expression:"!item.starting && !item.finished"}],staticClass:"pause-box",on:{click:t.onPause}},[e("i",{staticClass:"el-icon-video-pause"}),t._v("暂停推理 ")]):t._e()],2),e("div",{staticClass:"copy"},[e("a",{on:{click:function(e){return t.copy(i.chatContent)}}},[e("i",{staticClass:"el-icon-document-copy"})]),"assistant"!=i.chatRole?e("a",{staticClass:"star",on:{click:function(e){return t.collection(i)}}},[e("i",{staticClass:"el-icon-star-off"})]):t._e(),"assistant"==i.chatRole&&i.imgUrls&&i.imgUrls.length>0?e("div",{staticClass:"download",on:{click:function(e){return t.onDown(i,"img")}}},[e("i",{staticClass:"el-icon-download"})]):t._e(),"assistant"==i.chatRole&&i.videoUrls&&i.videoUrls.length>0?e("div",{staticClass:"download",on:{click:function(e){return t.onDown(i,"video")}}},[e("i",{staticClass:"el-icon-download"})]):t._e()])])})),0)},yi=[],bi={name:"chat-box",props:{_chatDialogs:Array},created(){this.session=y()},data(){return{session:null}},methods:{copy(t){this.$copyText(t),this.$message({message:"已复制",type:"success"})},async collection(t){let e=await K({msgId:t.msgId});0===e.code?this.$message({message:"收藏成功",type:"success"}):this.$message({message:"收藏失败",type:"error"})},generateRandomFileName(t){const e="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",i=10;let s="";for(let n=0;n<i;n++){const t=Math.floor(Math.random()*e.length);s+=e.charAt(t)}let a=t.lastIndexOf("."),o="";return o=-1===a?"":t.substring(a),s+o},onDown(t,e){let i="",s="";"img"==e&&(s=t.imgUrls[0],i=this.generateRandomFileName(t.imgUrls[0])),"video"==e&&(s=t.videoUrls[0],i=this.generateRandomFileName(t.videoUrls[0]));let a=document.createElement("a");a.href=s,a.download=i,a.target="_blank",a.rel="noopener noreferrer",a.click()},onPause(){this.$emit("onPause")}}},wi=bi,Ii=(0,wt.A)(wi,fi,yi,!1,null,"22a4b864",null),xi=Ii.exports,ki={name:"layout-chat-main",components:{ChatBox:xi},async created(){this.models=await N({is_all_pc:1}),this.baseRoles=await q(),this.models.push({name:"通用AI模型",value:""}),this.baseRoles.push({name:"通用对话角色",value:"",id:0}),this.getHelpRoleList(),this.getAiVendorList(),this.getLunciHelpUserList(),document.addEventListener("click",this.handleClickOutside)},props:{_chatDialogs:Array,_chatingId:String,_chatObj:Object},data(){return{models:[],baseRoles:[],roles:[],selectRoleKey:0,selectRole:{id:"",name:"通用对话角色",type:"base"},msg:[],isNewGlobalMsg:!1,textarea:"",msgImgs:[],videos:[],imgInputVisible:!1,videoInputVisible:!1,isPrviewShow:!1,isHelp:!1,xzConfig:{isHelp:!1,helpInfo:{helpGuid:"",helpUser:{nickname:"",introduction:"",avatarUrl:""},isAbleReturn:!1,debugChatCount:0}},modelPopCofig:{aiPop:!1,modelVendorSign:"",modelType:"text",modelName:"获取中...",guid:"",isFirst:!0,isTabLoading:!1},reqAiConfig:{msgId:"",roleId:"",content_cate_id:"",aiModelGuid:"",roleType:"",chatScene:"default",sceneValue:"",maxOutputTokens:0,temperature:0,topP:0,systemPrompt:"",isMultiple:!0,isSend:!1,placeholder:"请输入您的问题或需求"},systemPrompt:"",modelDetail:{maxOutputTokens:0,modelType:"text",isImageRecognition:0,isVideoRecognition:0},aiModels:[],aiModelChildren:[],source:null,reqXzUserListConfig:{pageSize:6,page:1,total:0,list:[],isShowPop:!1}}},computed:{msgImgsCount(){return this.msgImgs.length},msgVideoCount(){return this.videos.length}},watch:{async _chatingId(){this.getChatLunciHelpInfo(),this.getHelpRoleList(),this.reqAiConfig.systemPrompt=this._chatObj.chatLunciPrompt}},methods:{async onSavePrompt(){await dt({chatLunciGuid:this._chatingId,chatLunciPrompt:this.systemPrompt}),this.$message({message:"保存成功",type:"success"}),this.reqAiConfig.systemPrompt=this.systemPrompt,this.isPrviewShow=!1},onOpenSavePop(){this.systemPrompt=this.reqAiConfig.systemPrompt,this.isPrviewShow=!0},async onChangeXzStatus(t){if("end"===t)try{let e=await ct({chatLunciGuid:this._chatingId,status:t});0==e.code?this.$message({message:"结束协助",type:"success"}):this.$message({message:e.msg,type:"info"}),this.getChatLunciHelpInfo()}catch(e){this.$message({message:e.msg,type:"info"})}else if(this.xzConfig.helpInfo.isAbleReturn)try{let e=await ct({chatLunciGuid:this._chatingId,status:t});0==e.code?this.$message({message:"返回成功",type:"success"}):this.$message({message:e.msg,type:"info"}),this.getChatLunciHelpInfo()}catch(e){this.$message({message:e.msg,type:"info"})}},async onInvite(t){try{let e=await lt({chatLunciGuid:this._chatingId,helpUserGuid:t});0==e.code?(this.$message({message:"邀请成功",type:"success"}),this.reqXzUserListConfig.isShowPop=!1,this.getChatLunciHelpInfo()):this.$message({message:e.msg,type:"info"})}catch(e){this.$message({message:e.msg,type:"info"})}},async getLunciHelpUserList(){let t=await nt(this.reqXzUserListConfig);this.reqXzUserListConfig.list=t.data.data,this.reqXzUserListConfig.total=t.data.total},async handlePageChang(t){this.reqXzUserListConfig.page=t,getLunciHelpUserList()},async getChatLunciHelpInfo(){let t=await ot({chatLunciGuid:this._chatingId});0===t.code&&(this.xzConfig.isHelp=t.data.isHelp,t.data.isHelp&&(this.xzConfig.helpInfo=t.data.helpInfo))},handleClickOutside(t){this.$refs.aiPopRef.contains(t.target)||(this.modelPopCofig.aiPop=!1)},uploadSuccess({code:t,data:e}){0==t&&this.msgImgs.push(e)},async getHelpRoleList(){let t=await Z({chatLunciGuid:this._chatingId}),e=[];this.helpList=t.data,t.data.forEach((t=>{let i={id:t.sysId,name:t.showRoleName,isHelp:!0,isDelete:t.isDelete,isEdit:t.isEdit};e.push(i)})),this.roles=e},onInvitation(){let t=this._chatObj.guid,e=`https://xy.deepcity.cn/app/chat?yqGuid=${t}`;this.$copyText(e),this.$message({message:"已复制邀请链接",type:"success"})},beforeRemove({response:t}){this.msgImgs=this.msgImgs.filter((e=>e!=t.data))},videoBeforeRemove({response:t}){this.videos=this.videos.filter((e=>e!=t.data))},videoUploadSuccess({code:t,data:e}){0==t&&this.videos.push(e)},handleExceed(){alert("超过文件上传数量")},choooseRole(t,e){t.isHelp&&(this.isHelp=!0),this.selectRoleKey=t.id,this.selectRole="xz"===e?this.roles.find((e=>e.id==t.id)):this.baseRoles.find((e=>e.id==t.id)),this.selectRole.type=e},onXzRole(){0===this.roles.length&&this.$message({message:"暂无可协助角色",type:"info"})},cleanDialogs(){this.$emit("clearChatDialogs"),z(this._chatingId)},onShowPop(t){"video"===t&&(this.videoInputVisible=!0,0===this.videos.length&&this.$nextTick((()=>{this.$refs.videoRef.clearFiles()}))),"img"===t&&(this.imgInputVisible=!0,0===this.msgImgs.length&&this.$nextTick((()=>{this.$refs.imgRef.clearFiles()})))},async send(){if(!this.reqAiConfig.isSend)return void this.$message({message:"AI模型错误或正在生成中，请稍后重试...",type:"warning"});if(!this.textarea)return;if("image"===this.modelDetail.modelType)return void this.waitingImage();if("video"===this.modelDetail.modelType)return void this.waitingVideo();let t=this.textarea;this.textarea="";let e="",i=this._chatDialogs.length;i>0&&(e=this._chatDialogs[i-1].msgId);let s=await G({role:"user",content:t,lastMsgId:this.reqAiConfig.isMultiple?e:"",aiModelGuid:this.modelPopCofig.guid,chatLunciGuid:this._chatingId,imgs:this.msgImgs,videos:this.videos});return this._chatDialogs.push({msgId:"new",chatRole:"user",chatContent:t,imgUrls:this.msgImgs,videoUrls:this.videos}),this._chatDialogs.push({msgId:"new",chatRole:"assistant",chatContent:"",starting:!0,finished:!1}),this.msgImgs=[],this.videos=[],this.toBottom(),await this.waitChat(s),!1},toBottom(){this.$nextTick((()=>{let t=document.getElementById("chatbox");t.scrollTop=t.scrollHeight}))},async waitChat(t){let e=this._chatDialogs[this._chatDialogs.length-1],i=this,s=`https://ai-api.deepcity.cn/square/api.chat/sendOpen?roleId=${this.selectRoleKey}&aiModelGuid=${this.modelPopCofig.guid}&msgId=${t}&roleType=${this.isHelp?"2":"1"}&maxOutputTokens=${this.reqAiConfig.maxOutputTokens}&temperature=${this.reqAiConfig.temperature}&topP=${this.reqAiConfig.topP}`;this.source=new EventSource(s),this.source.onmessage=async function({data:s}){if(s&&(e.starting=!1),"[DONE]"==s)return e.finished=!0,e.msgId=await G({role:"assistant",content:e.chatContent,aiModelGuid:i.modelPopCofig.guid,lastMsgId:i.reqAiConfig.isMultiple?t:"",chatLunciGuid:i._chatingId,model:i.selectModelKey}),void i.source.close();e.chatContent+=s.replace(/\\n/g,"\n")},this.source.onopen=function(t){console.log("Connection was opened")},this.source.onerror=async function(t){e.finished=!0,i.source.close()}},onPause(){let t=this._chatDialogs[this._chatDialogs.length-1];this.source.close(),t.finished=!0},async waitingImage(){let t=this.textarea;this.textarea="";let e=await G({role:"user",content:t,lastMsgId:"",contentType:"text",aiModelGuid:this.modelPopCofig.guid,chatLunciGuid:this._chatingId});this._chatDialogs.push({msgId:"new",chatRole:"user",chatContent:t}),this._chatDialogs.push({msgId:"new",chatRole:"assistant",chatContent:"",starting:!0,finished:!0}),this.toBottom();let i=await j({imgText:t,aiModelGuid:this.modelPopCofig.guid});if(i){let t=this._chatDialogs[this._chatDialogs.length-1];t.starting=!1,t.imgUrls=[i],t.msgId=await G({role:"assistant",content:i,contentType:"img",aiModelGuid:this.modelPopCofig.guid,lastMsgId:this.reqAiConfig.isMultiple?e:"",chatLunciGuid:this._chatingId})}},async waitingVideo(){let t=this.textarea;this.textarea="";let e=await G({role:"user",content:t,lastMsgId:"",contentType:"text",aiModelGuid:this.modelPopCofig.guid,chatLunciGuid:this._chatingId});this._chatDialogs.push({msgId:"new",chatRole:"user",chatContent:t}),this._chatDialogs.push({msgId:"new",chatRole:"assistant",chatContent:"",starting:!0,finished:!0}),this.toBottom();let i=await H({userPrompt:t,aiModelGuid:this.modelPopCofig.guid});this.$emit("changeOnChatState",!1),0==i.code&&(this.queryGenVideo(i.data.orderNo,e),this.reqAiConfig.isSend=!1)},queryGenVideo(t,e){let i=setInterval((async()=>{let s=await X({orderNo:t});if(0==s.code){if("success"===s.data.status){this.reqAiConfig.isSend=!0,this.$emit("changeOnChatState",!0),clearInterval(i);let t=this._chatDialogs[this._chatDialogs.length-1];t.starting=!1,t.videoUrls=[s.data.videoUrl],t.msgId=await G({role:"assistant",content:s.data.videoUrl,contentType:"video",aiModelGuid:this.modelPopCofig.guid,lastMsgId:this.reqAiConfig.isMultiple?e:"",chatLunciGuid:this._chatingId})}}else s.data.status}),5e3)},async getAiVendorList(){let t=await st();this.aiModels=t.data,t.data.length>0&&(this.modelPopCofig.modelVendorSign=t.data[0].sign,this.getAiVendorModelList())},async getAiVendorModelList(){this.aiModelChildren=[],this.modelPopCofig.isTabLoading=!0;let t=await at(this.modelPopCofig);this.modelPopCofig.isTabLoading=!1,this.aiModelChildren=t.data,t.data.length>0&&this.modelPopCofig.isFirst&&(this.reqAiConfig.isSend=!0,this.modelDetail=t.data[0],this.initChatOptions(t.data[0]),this.modelPopCofig.guid=t.data[0].guid,this.modelPopCofig.modelName=t.data[0].modelName,this.modelPopCofig.isFirst=!1)},onModelBtn(){this.modelPopCofig.aiPop=!this.modelPopCofig.aiPop},onModelChange(t){this.modelPopCofig.modelVendorSign=t.sign,this.getAiVendorModelList()},onModelTypeChange(t){this.modelPopCofig.modelType=t,this.getAiVendorModelList()},onModelChoose(t){this.modelDetail=t,this.initChatOptions(t),this.modelPopCofig.modelName=t.modelName,this.modelPopCofig.guid=t.guid,this.modelPopCofig.aiPop=!1},initChatOptions(t){switch(this.reqAiConfig.maxOutputTokens=Number((t.maxOutputTokens/2).toFixed()),this.reqAiConfig.topP=.6,this.reqAiConfig.temperature=.7,t.modelType){case"text":this.reqAiConfig.placeholder="请输入你的问题和需求";break;case"image":this.reqAiConfig.placeholder="请描述你想生成的图片(生成图片需等待1-2分钟)";break;case"video":this.reqAiConfig.placeholder="请描述你想生成的视频(生成视频需等待较长时间，生成时请耐心等待⌛️)";break;default:break}}},beforeDestroy(){document.removeEventListener("click",this.handleClickOutside)}},Ri=ki,Pi=(0,wt.A)(Ri,Ci,Ai,!1,null,"1986c482",null),Mi=Pi.exports,Li={name:"layout-chat",components:{LayoutChatMain:Mi,LayoutChatLeft:vi},async mounted(){await this.loadChatList(),this.chatingId=await B()},data(){return{input:"",chatList:[],chatingId:"",chatDialogs:[],onChatState:!0}},methods:{clearChatDialogs(){this.chatDialogs=[]},async loadChatList(){this.chatList=await O(),this.chatList.forEach((t=>{t.visible=!0}))},async createChat(){await E(),await this.loadChatList(),this.chatingId=await B()},changeOnChatState(t){this.onChatState=t}},computed:{chatObj(){let t=this.chatList.find((t=>t.guid==this.chatingId));return t||null}},watch:{async chatingId(t){this.chatDialogs=[],this.$nextTick((async()=>{let e=await U({chatLunciGuid:t});e.forEach((t=>{t.finished=!0,"assistant"===t.chatRole&&("img"==t.contentType&&(t.imgUrls=[t.chatContent],t.chatContent=""),"video"==t.contentType&&(t.videoUrls=[t.chatContent],t.chatContent=""))})),this.chatDialogs=e}))}}},Si=Li,Di=(0,wt.A)(Si,ri,di,!1,null,null,null),Ti=Di.exports,Ei=function(){var t=this,e=t._self._c;return e("el-main",{staticClass:"layout-gallery"},[e("el-container",[e("layout-gallery-main")],1)],1)},Bi=[],Oi={name:"layout-gallery",components:{LayoutGalleryMain:ai,LayoutDrawLeft:Qe},data(){return{}},computed:{},methods:{}},Ui=Oi,Vi=(0,wt.A)(Ui,Ei,Bi,!1,null,null,null),Gi=Vi.exports,Fi=function(){var t=this,e=t._self._c;return e("el-main",{staticClass:"layout-draw"},[e("el-container",[e("layout-tools-left",{on:{changeActiveClassId:t.createChat,changeAgentId:t.createAgent}}),t.isAgent?e("layout-tools-agent",{attrs:{agentId:t.agentId,name:t.agentName}}):e("layout-tools-main",{attrs:{toolId:t.toolId}})],1)],1)},Ni=[],qi=function(){var t=this,e=t._self._c;return e("div",{staticClass:"layout-tools-left"},[e("div",{staticClass:"tab-box"},t._l(t.classList,(function(i,s){return e("div",{key:s,class:["tab-item",{active:t.sceneListId===i.id}],on:{click:function(e){return t.onChengOptions(i)}}},[t._v(" "+t._s(i.title)+" ")])})),0),1===t.sceneListId?e("div",{staticClass:"content-list"},t._l(t.agentList,(function(i){return e("div",{class:["item-box",{active:t.activeClassId===i.guid}]},[e("img",{staticClass:"image",attrs:{src:i.agentCover},on:{click:function(e){return t.onAgentItem(i)}}}),e("div",{staticClass:"title-box"},[e("div",{staticClass:"title"},[t._v(" "+t._s(i.agentName)+" ")])])])})),0):t._e(),e("div",{staticClass:"content-list"},t._l(t.contentList,(function(i){return e("div",{class:["item-box",{active:t.activeClassId===i.id}]},[e("img",{staticClass:"image",attrs:{src:i.image},on:{click:function(e){return t.onItem(i)}}}),e("div",{staticClass:"title-box"},[e("div",{staticClass:"collect",on:{click:function(e){return t.onCollect(i)}}},[e("i",{class:["el-icon-star-off",{"el-icon-star-on":i.isCollect}]})]),e("div",{staticClass:"title"},[t._v(" "+t._s(i.title)+" ")])])])})),0)])},zi=[];const ji=t=>S.post("square/api.ContentGeneration/copywritingCategory",t),Hi=t=>S.post("square/api.ContentGeneration/categoryContentList",t),Xi=t=>S.post("square/api.ContentGeneration/catgoryContentInfo",t),Qi=t=>S.post("user/api.userWork/saveTextContent",t),Yi=t=>S.post("square/api.ContentGeneration/userCollectionCate",t),_i=t=>S.post("square/api.ContentGeneration/userCancelCate",t),Ji=t=>S.post("square/api.ContentGeneration/userCollectionCateList",t),Wi=t=>S.post("square/api.chat/saveMsgV2",t),Ki=t=>S.post("/square/api.chat/getAgentList",t);var Zi,$i,ts={name:"layout-chat-left",props:{},data(){return{sceneListId:"",activeClassId:"",classList:[{id:0,title:"我的收藏"},{id:1,title:"扣子智能体"}],contentList:[],agentList:[]}},mounted(){this.getClassList()},methods:{async getClassList(){let t=await ji({cateType:"text",merchantGuid:this.$store.state.merchantGuid});this.classList.push(...t.data),this.sceneListId=1,this.getAgentList()},async getAgentList(){let t=await Ki({agentVendorSign:"",agentType:"text"});this.agentList=t.data},onChengOptions(t){this.sceneListId=t.id,0===t.id?this.collectClassList():1===t.id?this.getAgentList():this.getClassContentList()},async collectClassList(){let t=await Ji({cateType:"text"}),e=[];t.data.forEach((t=>{t.categoryInfo&&(t.categoryInfo.isCollect=!0,e.push(t.categoryInfo))})),this.contentList=e},async getClassContentList(){let t={cateId:this.sceneListId},e=await Hi(t);this.contentList=e.data,!this.activeClassId&&e.data.length>0&&(this.activeClassId=e.data[0].id,this.$emit("changeActiveClassId",this.activeClassId))},onItem(t){this.activeClassId=t.id,this.$emit("changeActiveClassId",t.id)},onAgentItem(t){this.activeClassId=t.guid,this.$emit("changeAgentId",t)},async onCollect(t){0===this.activeClassId||t.isCollect?(await _i({cateId:t.id}),this.$message({message:"取消收藏",type:"success"})):(console.log(t,"itemitemitemitem"),await Yi({cateId:t.id}),this.$message({message:"收藏成功",type:"success"})),0===this.activeClassId?this.collectClassList():this.getClassContentList()}}},es=ts,is=(0,wt.A)(es,qi,zi,!1,null,null,null),ss=is.exports,as=function(){var t=this,e=t._self._c;return e("div",{staticClass:"layout-chat-main"},[e("div",{staticClass:"chat-header"},[t._v(t._s(t.classDetail.title))]),e("chat-box",{ref:"chatBox",attrs:{id:"chatbox",_chatDialogs:t.chatDialogs}}),e("div",{staticClass:"chat-input"},[e("div",{staticClass:"opt-panel mb4"},[e("div",{staticClass:"clean-chat-btn mr4",on:{click:t.cleanDialogs}},[t._v("清除聊天记录")])]),e("div",{staticClass:"chat-input-box"},[e("div",{staticClass:"input-box"},[e("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请输入内容",resize:"none"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.send.apply(null,arguments)}},model:{value:t.msgText,callback:function(e){t.msgText=e},expression:"msgText"}})],1),e("div",{staticClass:"send-icon"},[e("img",{staticClass:"icon",attrs:{src:i(8681)},on:{click:t.send}})])])])],1)},os=[],ns={name:"layout-tools-main",components:{ChatBox:xi},async created(){},props:{toolId:Number},data(){return{msgText:"",sendReq:{msgId:"",content_cate_id:0,background_id:0,isSend:!1},chatDialogs:[],classDetail:{title:""},timeoutVar:0,msgId:"",msgReqParame:{role:"user",content:"",lastMsgId:"",aiModelGuid:""},msgReqAiParame:{role:"assistant",content:"",lastMsgId:"",aiModelGuid:""},saveTextReq:{title:"",workResult:"",copywritingCategoryId:"",msgId:""},isNewGlobalMsg:!1}},computed:{},watch:{async toolId(t){console.log(t,"---dsadasdaaaaaaaaaa"),this.sendReq.content_cate_id=t,this.chatDialogs=[];try{let e=await Xi({cateId:t});this.classDetail=e.data,this.sendReq.isSend=!0}catch(e){}}},methods:{cleanDialogs(){this.chatDialogs=[]},async send(){this.submitQuestion(this.msgText)},async submitQuestion(t){if(this.sendReq.isSend)if(0!==t.trim().length)if(this.isNewGlobalMsg)this.$message({message:"AI正在回答中 ...",type:"warning"});else{this.isNewGlobalMsg=!0,this.msgReqParame.content=t,this.msgReqParame.aiModelGuid=this.classDetail.ai_model,this.saveTextReq.title=t;try{this.msgReqParame.lastMsgId=this.msgId;let e=await Wi(this.msgReqParame);this.chatDialogs.push({msgId:"new",chatRole:"user",chatContent:t,imgUrls:[]}),this.chatDialogs.push({msgId:"new",chatRole:"assistant",chatContent:"",starting:!0,finished:!1});let i=this.chatDialogs[this.chatDialogs.length-1];if(0!==e.code)return i.starting=!1,void(i.chatContent="算力繁忙，请稍候重试");this.msgText="",this.sendReq.msgId=e.data.msgId,this.msgId=e.data.msgId,this.waitChat(e.data.msgId)}catch(e){this.sendReq.msgId="";let t=this.chatDialogs[this.chatDialogs.length-1];return t.starting=!1,t.chatContent="算力繁忙，请稍候重试",this.isNewGlobalMsg=!1,void(this.msgText="")}}else this.$message({message:"请输入问题 ...",type:"warning"});else this.$message({message:"AI模型错误或正在生成中，请稍后重试...",type:"warning"})},async waitChat(t){let e=this.chatDialogs[this.chatDialogs.length-1],i=this,s=`https://ai-api.deepcity.cn/square/api.chat/sendOpen?content_cate_id=${this.sendReq.content_cate_id}&msgId=${t}&aiModelGuid=${this.classDetail.ai_model}`;const a=new EventSource(s);a.onmessage=async function({data:t}){if(t&&(e.starting=!1),"[DONE]"==t){e.finished=!0,i.msgReqAiParame.content=e.chatContent,i.msgReqAiParame.lastMsgId=i.msgId,i.msgReqAiParame.aiModelGuid=i.classDetail.ai_model;let t=await Wi(i.msgReqAiParame);1===i.classDetail.able_lunci&&(i.msgId=t.data.msgId),i.saveTextReq.msgId=t.data.msgId,i.isNewGlobalMsg=!1,a.close()}else e.chatContent+=t.replace(/\\n/g,"\n")},a.onopen=function(t){console.log("Connection was opened")},a.onerror=async function(t){a.close()}},async saveToolMsgContent(){await Qi(this.saveTextReq)},toBottom(){this.$nextTick((()=>{let t=document.getElementById("chatbox");t.scrollTop=t.scrollHeight}))}}},ls=ns,cs=(0,wt.A)(ls,as,os,!1,null,"295b049a",null),rs=cs.exports,ds=function(){var t=this,e=t._self._c;return e("div",{staticClass:"layout-chat-main"},[e("div",{staticClass:"chat-header"},[t._v(t._s(t.name))]),e("chat-box",{ref:"chatBox",attrs:{id:"chatbox",_chatDialogs:t.chatDialogs}}),e("div",{staticClass:"chat-input"},[e("div",{staticClass:"opt-panel mb4"},[e("div",{staticClass:"clean-chat-btn mr4",on:{click:t.cleanDialogs}},[t._v("清除聊天记录")])]),e("div",{staticClass:"chat-input-box"},[e("div",{staticClass:"input-box"},[e("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请输入内容",resize:"none"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.send.apply(null,arguments)}},model:{value:t.msgText,callback:function(e){t.msgText=e},expression:"msgText"}})],1),e("div",{staticClass:"send-icon"},[e("img",{staticClass:"icon",attrs:{src:i(8681)},on:{click:t.send}})])])])],1)},hs=[],us={name:"layout-tools-agent",components:{ChatBox:xi},async created(){},props:{agentId:String,name:String},data(){return{msgText:"",sendReq:{msgId:"",content_cate_id:0,background_id:0,isSend:!0},chatDialogs:[],classDetail:{title:""},timeoutVar:0,msgId:"",msgReqParame:{role:"user",content:"",lastMsgId:"",aiModelGuid:""},msgReqAiParame:{role:"assistant",content:"",lastMsgId:"",aiModelGuid:""},saveTextReq:{title:"",workResult:"",copywritingCategoryId:"",msgId:""},isNewGlobalMsg:!1,conversation_id:""}},computed:{},methods:{cleanDialogs(){this.chatDialogs=[]},async send(){this.submitQuestion(this.msgText)},async submitQuestion(t){if(this.sendReq.isSend)if(0!==t.trim().length)if(this.isNewGlobalMsg)this.$message({message:"AI正在回答中 ...",type:"warning"});else{this.isNewGlobalMsg=!0,this.msgReqParame.content=t,this.saveTextReq.title=t;try{this.msgReqParame.lastMsgId=this.msgId;let e=await Wi(this.msgReqParame);this.chatDialogs.push({msgId:"new",chatRole:"user",chatContent:t,imgUrls:[]}),this.chatDialogs.push({msgId:"new",chatRole:"assistant",chatContent:"",starting:!0,finished:!1});let i=this.chatDialogs[this.chatDialogs.length-1];if(0!==e.code)return i.starting=!1,void(i.chatContent="算力繁忙，请稍候重试");this.msgText="",this.sendReq.msgId=e.data.msgId,this.msgId=e.data.msgId,this.waitChat(e.data.msgId)}catch(e){this.sendReq.msgId="";let t=this.chatDialogs[this.chatDialogs.length-1];return t.starting=!1,t.chatContent="算力繁忙，请稍候重试",this.isNewGlobalMsg=!1,void(this.msgText="")}}else this.$message({message:"请输入问题 ...",type:"warning"});else this.$message({message:"AI模型错误或正在生成中，请稍后重试...",type:"warning"})},async waitChat(t){let e=this.chatDialogs[this.chatDialogs.length-1],i=this,s=`https://ai-api.deepcity.cn/square/api.chat/sendAgentOpen?msgId=${t}&aiAgentGuid=${this.agentId}&userGuid=${this.$store.state.userGuid}&conversation_id=${this.conversation_id}&merchantGuid=${this.$store.state.merchantGuid}`;const a=new EventSource(s);a.onmessage=async function({lastEventId:t,data:s}){if(s&&(e.starting=!1),"[DONE]"==s){i.conversation_id=t,e.finished=!0,i.msgReqAiParame.content=e.chatContent,i.msgReqAiParame.lastMsgId=i.msgId;let s=await Wi(i.msgReqAiParame);1===i.classDetail.able_lunci&&(i.msgId=s.data.msgId),i.saveTextReq.msgId=s.data.msgId,i.isNewGlobalMsg=!1,a.close()}else e.chatContent+=s.replace(/\\n/g,"\n")},a.onopen=function(t){console.log("Connection was opened")},a.onerror=async function(t){a.close()}},async saveToolMsgContent(){await Qi(this.saveTextReq)},toBottom(){this.$nextTick((()=>{let t=document.getElementById("chatbox");t.scrollTop=t.scrollHeight}))}}},ps=us,gs=(0,wt.A)(ps,ds,hs,!1,null,"119b3765",null),ms=gs.exports,vs={name:"layout-tools",components:{LayoutToolsLeft:ss,LayoutToolsMain:rs,LayoutToolsAgent:ms},async mounted(){},data(){return{genImages:[],status:"0",config:{},toolId:0,agentId:0,agentName:"",isAgent:!1}},methods:{createChat(t){this.isAgent=!1,this.$nextTick((()=>{this.toolId=t}))},createAgent(t){this.toolId=0,this.isAgent=!0,this.agentId=t.guid,this.agentName=t.agentName}}},Cs=vs,As=(0,wt.A)(Cs,Fi,Ni,!1,null,null,null),fs=As.exports,ys={name:"layout",components:{LayoutGallery:Gi,LayoutChat:Ti,LayoutDraw:ci,LayoutHeader:Ie,LayoutSider:xt,LayoutTools:fs},mounted(){},data(){let t=this.$route.params.pageName;return{pageName:t}}},bs=ys,ws=(0,wt.A)(bs,n,l,!1,null,null,null),Is=ws.exports,xs=function(){var t=this,e=t._self._c;return e("div",{staticClass:"login-register"},[e("div",{staticClass:"bg-box"},[e("div",{staticClass:"sub-box"},[e("div",{staticClass:"logo-title"},[t._v(t._s(t.pcConfig.title))]),e("div",{staticClass:"logo-label"},[t._v("一个人，靠AI搞定所有工作，效率直接拉满。")])])]),e("div",{staticClass:"flex flex-center login-box"},[e("div",{staticClass:"login-panel"},[e("div",{staticClass:"tab-box"},[e("div",{staticClass:"tab-title"},[e("div",{class:["item",{active:"qrcode"===t.activeTab}],on:{click:function(e){return t.tabClick("qrcode")}}},[t._v(" 扫码登录 ")]),e("div",{class:["item",{active:"login"===t.activeTab}],on:{click:function(e){return t.tabClick("login")}}},[t._v(" 验证码登录 ")])]),e("div",{staticClass:"tab-content"},["qrcode"===t.activeTab?e("div",{staticClass:"item"},[e("div",{staticClass:"qrcode-container"},[e("el-image",{attrs:{src:t.qrImage}},[e("div",{staticClass:"image-slot",staticStyle:{width:"130px",height:"130px",background:"#f1f5f9","font-size":"12px",color:"var(--text-black-light)"},attrs:{slot:"placeholder"},slot:"placeholder"},[t._v(" 加载中"),e("span",{staticClass:"dot"},[t._v("...")])])]),t.scanSuccess?e("div",{staticClass:"scan-tip"},[t._v("扫码成功，请在手机上确认登录")]):e("div",{staticClass:"scan-tip",staticStyle:{color:"var(--text-black-light)"}},[t._v(" 请使用微信扫码登录 ")])],1)]):t._e(),"login"===t.activeTab?e("div",{staticClass:"item"},[e("el-form",{ref:"loginForm",attrs:{model:t.loginForm,"label-width":"80px"}},[e("el-input",{attrs:{size:"medium",placeholder:"请输入手机号码"},model:{value:t.loginForm.phone,callback:function(e){t.$set(t.loginForm,"phone",e)},expression:"loginForm.phone"}},[e("template",{slot:"prefix"},[e("div",{staticClass:"login-icon-box"},[e("img",{staticClass:"icon",attrs:{src:i(6428)}})])])],2),e("div",{staticClass:"flex mt20"},[e("el-input",{attrs:{size:"medium",placeholder:"输入验证码【发送方：米蜗城市】"},model:{value:t.loginForm.code,callback:function(e){t.$set(t.loginForm,"code",e)},expression:"loginForm.code"}},[e("template",{slot:"prefix"},[e("div",{staticClass:"login-icon-box"},[e("img",{staticClass:"icon",attrs:{src:i(4526)}})])]),e("template",{slot:"suffix"},[e("div",{staticClass:"login-yzm-box"},[e("a",{directives:[{name:"show",rawName:"v-show",value:t.countdown<1,expression:"countdown < 1"}],staticClass:"send-code",on:{click:t.sendCode}},[t._v("发送验证码")]),e("a",{directives:[{name:"show",rawName:"v-show",value:t.countdown>0,expression:"countdown > 0"}],staticClass:"send-code"},[t._v(t._s(t.countdown)+"秒后重试")])])])],2)],1),e("div",{staticClass:"mt20 flex"},[e("div",{staticClass:"login-btn",on:{click:t.login}},[t._v("登录")])])],1)],1):t._e()])]),t._m(0)])])])},ks=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"copy-right"},[e("span",{staticClass:"title"},[t._v("一城一智(广东)科技有限公司版权所有 ")]),e("a",{attrs:{href:"https://beian.miit.gov.cn/",target:"_blank"}},[t._v(" 粤ICP备2020076772号-19")])])}],Rs=(i(4603),i(7566),i(8721),i(2289)),Ps={name:"login",components:{Carousel3d:Rs.Carousel3d,Slide:Rs.Slide},mounted(){let t=new URLSearchParams(window.location.href.split("?")[1]);if(t.get("inviteCode")&&(this.isInvite=!0,this.inviteCode=t.get("inviteCode"),this.inviteType=t.get("type")),t.get("h")){let e=t.get("h");this.aiMouseLogin(e)}this.getMerchantGuid()},destroyed(){this.qrInterval&&clearInterval(this.qrInterval)},data(){return{activeTab:"qrcode",loginForm:{phone:"",code:""},countdown:0,scanSuccess:!1,slides:4,qrImage:"",qrCode:"",qrInterval:null,isInvite:!1,inviteCode:"",inviteType:"",pcConfig:{title:""},merchantGuid:""}},methods:{async getMerchantGuid(){let t=window.location.origin,e=await le({pcUrl:t});this.merchantGuid=e.data.merchantGuid,this.$store.commit("setMerchantGuid",e.data.merchantGuid),this.$store.commit("setMerchantGuid","e108201b02ae42e686bcc4c302cbbd11"),this.getPcShowConfig(),this.generateQrcode()},async tabClick(t){this.activeTab=t,"qrcode"==t?this.startScan():(clearInterval(this.qrInterval),this.qrInterval=null)},async aiMouseLogin(t){let e=await ie({h:t});if(0===e.code){clearInterval(this.qrInterval),this.qrInterval=null;let t=e.data.token;this.$message({message:"AI鼠标已识别正在登录....",type:"success"}),A(t),setTimeout((()=>{this.$router.push("/app/chat")}),1e3)}else this.$message({message:e.msg,type:"error"})},async getPcShowConfig(){let{data:t}=await ne({merchantGuid:this.merchantGuid});this.pcConfig=t,document.title=t.title?t.title:"AI龙龙，一个人的工作站"},async login(){let{phone:t,code:e}=this.loginForm,i=null;i=this.isInvite?{phone:t,smsCode:e,merchantGuid:this.merchantGuid,invitationText:this.inviteCode,invitationType:this.inviteType}:{phone:t,smsCode:e,merchantGuid:this.merchantGuid};let s=await Lt(i);if(0===s.code){let t=s.data;A(t),clearInterval(this.qrInterval),setTimeout((()=>{this.$router.push("/app/chat")}),500)}else this.$message({message:s.msg,type:"error"})},async generateQrcode(){let t=window.location.origin,e=null;e=this.isInvite?{invitationType:this.inviteType,invitationText:this.inviteCode,merchantGuid:this.merchantGuid,pcUrl:t}:{merchantGuid:this.merchantGuid,pcUrl:t};let{codeImg:i,loginCode:s}=await Ut(e);this.qrImage=i,this.qrCode=s,this.startScan()},startScan(){this.qrInterval=setInterval((async()=>{let t=await Vt(this.qrCode);if(t.data.isLogin){let{isLogin:e,token:i}=t.data;i&&(clearInterval(this.qrInterval),A(i),this.$router.push("/app/chat"))}}),3e3)},async sendCode(){let{phone:t}=this.loginForm;if(t){await St({imageCode:"Jwjoz",phone:t,from:"xiaoyi"}),this.countdown=60,this.$message({message:"验证码已发送",type:"success"});let e=setInterval((()=>{this.countdown--,this.countdown<=0&&clearInterval(e)}),1e3)}}}},Ms=Ps,Ls=(0,wt.A)(Ms,xs,ks,!1,null,null,null),Ss=Ls.exports,Ds=i(7704),Ts={name:"App",components:{Login:Ss,Layout:Is},created(){if("Android"===Ds.osName||"iOS"===Ds.osName){let t=confirm("检测到你正在使用手机端访问PC工作台，即将跳转到手机端工作台，是否继续？");t&&(window.location.href="https://ai.deepcity.cn/")}}},Es=Ts,Bs=(0,wt.A)(Es,a,o,!1,null,null,null),Os=Bs.exports,Us=i(2879),Vs={name:"FeatherIcon",functional:!0,props:{icon:{required:!0,type:[String,Object]},size:{type:String,default:"14"},badge:{type:[String,Object,Number],default:null},badgeClasses:{type:[String,Object,Array],default:"badge-primary"}},render(t,{props:e,data:i}){const s=t(Us[e.icon],{props:{size:e.size},...i});if(!e.badge)return s;const a=t("span",{staticClass:"badge badge-up badge-pill",class:e.badgeClasses},[e.badge]);return t("span",{staticClass:"feather-icon position-relative"},[s,a])}},Gs=Vs,Fs=(0,wt.A)(Gs,Zi,$i,!1,null,null,null),Ns=Fs.exports,qs=i(9736),zs=i(166),js=i.n(zs),Hs=i(7214),Xs=i.n(Hs),Qs=i(4751),Ys=i.n(Qs),_s=i(5074),Js=i.n(_s),Ws=i(7943),Ks=i.n(Ws),Zs=i(6178),$s=function(){var t=this,e=t._self._c;return e("el-main",{staticClass:"layout-chat"},[e("el-container",[e("layout-cooperation-left",{attrs:{_chatList:t.chatList,_chatingId:t.guid,_onChatState:t.onChatState},on:{changeActiveId:t.createChat,exitCooperation:t.helpLunciList}}),e("layout-cooperation-main",{attrs:{_chatObj:t.chatObj,_chatingId:t.guid,_chatDialogs:t.chatDialogs},on:{clearChatDialogs:t.clearChatDialogs}})],1)],1)},ta=[],ea=function(){var t=this,e=t._self._c;return e("div",{staticClass:"layout-chat-left"},[e("div",{staticClass:"add-box flex"},[e("el-input",{staticClass:"mr4",attrs:{size:"large",placeholder:"请输入邀请链接"},model:{value:t.addPath,callback:function(e){t.addPath=e},expression:"addPath"}}),e("el-button",{attrs:{size:"large",type:"primary"},on:{click:t.createChat}},[e("i",{staticClass:"el-icon-plus"})])],1),!t._chatList.length>0?e("div",{staticClass:"none-box"},[t._v("暂无协助空间")]):t._e(),e("div",{staticClass:"chats-panel pt10"},t._l(t._chatList,(function(i){return e("div",{key:i.guid,staticClass:"chat-item",class:{active:i.chatLunciGuid==t._chatingId}},[e("div",{staticClass:"chat-name-box",on:{click:function(e){return t.onItem(i)}}},[e("div",{staticClass:"name"},[e("span",[t._v(t._s(i.lunciInfo.chatLunciTitle))])]),e("div",{staticClass:"date"},[t._v(t._s(i.modifyTime))])]),e("div",{staticClass:"chat-icon-box"},[e("div",{staticClass:"opts"},[e("el-popconfirm",{attrs:{title:"确定退出协作吗 ？"},on:{confirm:function(e){return t.onExit(i)}}},[e("a",{attrs:{slot:"reference"},slot:"reference"},[e("i",{staticClass:"el-icon-delete"})])])],1)])])})),0)])},ia=[],sa={name:"layout-cooperation-left",props:{_chatingId:String,_chatList:Array,_onChatState:Boolean},data(){return{addPath:""}},computed:{},mounted(){},methods:{createChat(){if(0===this.addPath.trim().length)return;let t=new URLSearchParams(this.addPath.split("?")[1]);t.get("yqGuid")&&this.joinLunci(t.get("yqGuid"))},async joinLunci(t){let e=await Tt({chatLunciGuid:t});if(0!==e.code)return this.$message({type:"info",message:e.msg||"获取协作聊天失败"}),void localStorage.setItem("yqGuid","");this.$emit("exitCooperation")},async onExit(t){this.$confirm("是否退出该协助创作","退出协助",{confirmButtonText:"确认",cancelButtonText:"取消"}).then((async()=>{await it({chatLunciGuid:t.chatLunciGuid}),this.$message({message:"退出成功",type:"success"}),this.$emit("exitCooperation")})).catch((()=>{this.$message({type:"info",message:"已取消"})}))},onItem(t){t.chatLunciGuid!==this._chatingId&&(this._onChatState?this.$emit("changeActiveId",t.chatLunciGuid):this.$message({showClose:!0,message:"有生成任务进行中，暂不可切换",type:"warning"}))}}},aa=sa,oa=(0,wt.A)(aa,ea,ia,!1,null,"348848bd",null),na=oa.exports,la=function(){var t=this,e=t._self._c;return e("div",{staticClass:"container"},[e("div",{staticClass:"layout-chat-main"},[t._chatObj?e("div",{staticClass:"chat-header"},[e("div",{staticClass:"left-box"},[1==t._chatObj.helpStatus?e("div",{staticClass:"ml10 completed-btn top-public-btn"},[e("img",{staticClass:"icon",attrs:{src:i(2799)}}),t._v(" 已完成 ")]):t._e(),0==t._chatObj.helpStatus?e("div",{staticClass:"ml10 no-completed-btn top-public-btn"},[e("img",{staticClass:"icon",attrs:{src:i(8737)}}),t._v(" 协助中 ")]):t._e()]),1==!t._chatObj.helpStatus?e("div",{staticClass:"right-box"},[e("el-popconfirm",{attrs:{title:"确认结束协助，是否继续？"},on:{confirm:function(e){return t.onChangeXzStatus("end")}}},[e("div",{staticClass:"end-btn top-public-btn ml10",attrs:{slot:"reference"},slot:"reference"},[e("img",{staticClass:"icon",attrs:{src:i(2473)}}),t._v("结束 ")])]),e("el-popconfirm",{attrs:{title:"确认返回协助，，是否继续？"},on:{confirm:function(e){return t.onChangeXzStatus("return")}}},[e("div",{staticClass:"back-btn top-public-btn ml10",attrs:{slot:"reference"},slot:"reference"},[e("img",{staticClass:"icon",attrs:{src:i(5681)}}),t._v(" 返回 ")])])],1):t._e()]):t._e(),e("chat-box",{ref:"chatBox",attrs:{id:"chatbox",_chatDialogs:t._chatDialogs},on:{onPause:t.onPause}}),e("div",{staticClass:"chat-input"},[e("div",{staticClass:"opt-panel"},[e("div",{ref:"aiPopRef",staticClass:"popper-box mr4"},[t.modelPopCofig.aiPop?e("div",{staticClass:"dropdown-box"},t._l(t.aiModels,(function(i){return e("div",{key:i.sign,class:["item",{active:t.modelPopCofig.modelVendorSign==i.sign}],on:{click:function(e){return t.onModelChange(i)}}},[t._v(" "+t._s(i.name)+" "),e("i",{staticClass:"el-icon-arrow-right el-icon--right ml-10"})])})),0):t._e(),t.modelPopCofig.aiPop?e("div",{staticClass:"children-dropdown-box"},[e("div",{staticClass:"tab"},[e("div",{staticClass:"item",class:{active:"text"===t.modelPopCofig.modelType},on:{click:function(e){return t.onModelTypeChange("text")}}},[t._v(" 文字 ")]),e("div",{staticClass:"item",class:{active:"image"===t.modelPopCofig.modelType},on:{click:function(e){return t.onModelTypeChange("image")}}},[t._v(" 图片 ")]),e("div",{staticClass:"item",class:{active:"video"===t.modelPopCofig.modelType},on:{click:function(e){return t.onModelTypeChange("video")}}},[t._v(" 视频 ")])]),e("div",{staticClass:"content"},[t._l(t.aiModelChildren,(function(i){return e("div",{staticClass:"item",class:{active:t.modelPopCofig.guid===i.guid},on:{click:function(e){return t.onModelChoose(i)}}},[e("div",{staticClass:"name"},[t._v(t._s(i.modelName))]),e("div",{staticClass:"price"},[t._v("价格："+t._s(0==i.usePrice?"免费":i.usePrice))]),e("el-tooltip",{attrs:{effect:"light",content:i.modelDesc,placement:"top-start"}},[e("div",{staticClass:"specific"},[t._v("特点："+t._s(i.modelDesc))])])],1)})),0===t.aiModelChildren.length||t.modelPopCofig.isTabLoading?e("div",{staticClass:"empty"},[t.modelPopCofig.isTabLoading?e("p",[t._v("加载中...")]):e("p",[t._v("暂无可用模型")])]):t._e()],2)]):t._e(),e("div",{class:["model-public-btn",{active:t.modelPopCofig.aiPop}],on:{click:t.onModelBtn}},[t._v(" 选择AI模型："+t._s(t.modelPopCofig.modelName)+" ")])]),e("el-dropdown",{staticClass:"mr4",attrs:{trigger:"click"}},[e("div",{staticClass:"role-select-btn"},[t._v(" 选择AI角色"),"base"===t.selectRole.type?e("span",[t._v("："+t._s(t.selectRole.name))]):t._e()]),e("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},t._l(t.baseRoles,(function(i){return e("el-dropdown-item",{key:i.id},[e("div",{staticClass:"flex role-drop-box"},[e("div",{staticClass:"role-drop-title mr-4",on:{click:function(e){return t.choooseRole(i,"base")}}},[t._v(" "+t._s(i.name)+" ")])])])})),1)],1),e("el-dropdown",{staticClass:"mr4",attrs:{trigger:"click"}},[e("div",{staticClass:"role-select-btn",on:{click:t.onXzRole}},[t._v(" 选择协助角色"),"xz"===t.selectRole.type?e("span",[t._v("："+t._s(t.selectRole.name))]):t._e()]),e("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},t._l(t.roles,(function(i){return e("el-dropdown-item",{key:i.id},[e("div",{staticClass:"flex role-drop-box"},[e("div",{staticClass:"role-drop-title mr-4",on:{click:function(e){return t.choooseRole(i,"xz")}}},[t._v(" "+t._s(i.name)+" ")]),i.isEdit?e("el-button",{attrs:{size:"mini",icon:"el-icon-edit"},on:{click:function(e){return t.onEditRole(i)}}}):t._e(),i.isDelete?e("el-button",{attrs:{size:"mini",icon:"el-icon-delete"},on:{click:function(e){return t.onDeleteRole(i)}}}):t._e()],1)])})),1)],1),e("div",{staticClass:"clean-chat-btn mr4",on:{click:t.addRolePop}},[t._v("新增协作角色")]),e("div",{staticClass:"clean-chat-btn mr4",on:{click:t.cleanDialogs}},[t._v("清除聊天记录")]),0==t.msgImgsCount&&1==t.modelDetail.isImageRecognition?e("div",{staticClass:"clean-chat-btn mr4",attrs:{disabled:0==t.modelDetail.isImageRecognition},on:{click:function(e){return t.onShowPop("img")}}},[e("i",{staticClass:"el-icon-upload2"}),t._v(" 上传图像 ")]):t._e(),t.msgImgsCount>0?e("el-badge",{staticClass:"ml4",attrs:{value:t.msgImgsCount}},[e("div",{staticClass:"clean-chat-btn mr4",attrs:{disabled:0==t.modelDetail.isImageRecognition},on:{click:function(e){return t.onShowPop("img")}}},[e("i",{staticClass:"el-icon-upload2"}),t._v(" 上传图像 ")])]):t._e(),0==t.msgVideoCount&&1==t.modelDetail.isVideoRecognition?e("div",{staticClass:"clean-chat-btn mr4",attrs:{disabled:0==t.modelDetail.isVideoRecognition},on:{click:function(e){return t.onShowPop("video")}}},[e("i",{staticClass:"el-icon-upload2"}),t._v(" 上传视频 ")]):t._e(),t.msgVideoCount>0?e("el-badge",{staticClass:"ml4",attrs:{value:t.msgVideoCount}},[e("div",{staticClass:"clean-chat-btn mr4",attrs:{disabled:0==t.modelDetail.isVideoRecognition},on:{click:function(e){return t.onShowPop("video")}}},[e("i",{staticClass:"el-icon-upload2"}),t._v(" 上传视频 ")])]):t._e()],1),e("div",{staticClass:"chat-input-box"},[e("div",{staticClass:"input-box"},[e("el-input",{attrs:{type:"textarea",autosize:{minRows:2,maxRows:12},placeholder:"请输入内容",resize:"none"},model:{value:t.textarea,callback:function(e){t.textarea=e},expression:"textarea"}})],1),e("div",{staticClass:"send-icon"},[e("img",{staticClass:"icon",attrs:{src:i(8681)},on:{click:t.send}})])])]),e("el-dialog",{attrs:{title:"图像上传",visible:t.imgInputVisible,width:"500px"},on:{"update:visible":function(e){t.imgInputVisible=e}}},[e("div",{staticClass:"center"},[e("el-upload",{attrs:{name:"img","list-type":"picture",limit:3,"on-success":t.uploadSuccess,"before-remove":t.beforeRemove,"on-exceed":t.handleExceed,drag:"",action:"https://ai-api.deepcity.cn/user/api.userinfo/uploadImg"}},[e("i",{staticClass:"el-icon-upload"}),e("div",{staticClass:"el-upload__text"},[t._v("将文件拖到此处，或"),e("em",[t._v("点击上传")])]),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("只能上传jpg/png文件，且不超过5mb")])])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{type:"primary"},on:{click:function(e){t.imgInputVisible=!1}}},[t._v("确 定")])],1)]),e("el-dialog",{attrs:{title:"视频上传",visible:t.videoInputVisible,width:"500px"},on:{"update:visible":function(e){t.videoInputVisible=e}}},[e("div",{staticClass:"center"},[e("el-upload",{ref:"videoRef",attrs:{name:"video","list-type":"text",limit:1,"on-success":t.videoUploadSuccess,"before-remove":t.videoBeforeRemove,accept:".mp4","on-exceed":t.handleExceed,drag:"",action:"https://ai-api.deepcity.cn/user/api.userinfo/uploadVideo"}},[e("i",{staticClass:"el-icon-upload"}),e("div",{staticClass:"el-upload__text"},[t._v("将文件拖到此处，或"),e("em",[t._v("点击上传")])]),e("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t._v("请上传MP4文件并且大小不超过100M的视频文件")])])],1),e("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("el-button",{attrs:{type:"primary"},on:{click:function(e){t.videoInputVisible=!1}}},[t._v("确 定")])],1)]),e("el-dialog",{attrs:{title:"预览",visible:t.isPrviewShow,"close-on-click-modal":!1,width:"800px"},on:{"update:visible":function(e){t.isPrviewShow=e}}},[e("el-input",{attrs:{type:"textarea",rows:20,placeholder:"请输入内容"},model:{value:t.textarea,callback:function(e){t.textarea=e},expression:"textarea"}})],1),e("el-dialog",{attrs:{title:t.addRoleFrom.isAdd?"新增协助角色":"修改协助角色",visible:t.isAddRoleShow,"close-on-click-modal":!1,width:"1000px"},on:{"update:visible":function(e){t.isAddRoleShow=e}}},[e("el-form",{ref:"addForm",staticClass:"demo-form-inline",attrs:{model:t.addRoleFrom,rules:t.rules,"label-width":"120px"}},[e("el-form-item",{attrs:{label:"新增方式",prop:"roleAddType"}},[t.addRoleFrom.isAdd?e("el-radio",{attrs:{label:"1"},model:{value:t.addRoleFrom.roleAddType,callback:function(e){t.$set(t.addRoleFrom,"roleAddType",e)},expression:"addRoleFrom.roleAddType"}},[t._v("AI员工导入")]):t._e(),e("el-radio",{attrs:{label:"2"},model:{value:t.addRoleFrom.roleAddType,callback:function(e){t.$set(t.addRoleFrom,"roleAddType",e)},expression:"addRoleFrom.roleAddType"}},[t._v("自定义角色")])],1),"1"===t.addRoleFrom.roleAddType?e("el-form-item",{attrs:{label:"选择角色",prop:"copywritingCategoryId"}},[e("el-select",{attrs:{placeholder:"请选择"},model:{value:t.addRoleFrom.copywritingCategoryId,callback:function(e){t.$set(t.addRoleFrom,"copywritingCategoryId",e)},expression:"addRoleFrom.copywritingCategoryId"}},t._l(t.classList,(function(t){return e("el-option",{key:t.id,attrs:{label:t.title,value:t.id}})})),1)],1):t._e(),"2"===t.addRoleFrom.roleAddType?e("el-form-item",{attrs:{label:"选择历史角色导入"}},[e("el-select",{attrs:{placeholder:"请选择"},on:{change:t.onChangeHistoryRole},model:{value:t.addRoleFrom.historyGuid,callback:function(e){t.$set(t.addRoleFrom,"historyGuid",e)},expression:"addRoleFrom.historyGuid"}},t._l(t.historyHelp,(function(t){return e("el-option",{key:t.guid,attrs:{label:t.chatgtpContentTitle,value:t}})})),1)],1):t._e(),"2"===t.addRoleFrom.roleAddType?e("el-form-item",{attrs:{label:"自定义角色名",prop:"chatgtpContentTitle"}},[e("el-input",{model:{value:t.addRoleFrom.chatgtpContentTitle,callback:function(e){t.$set(t.addRoleFrom,"chatgtpContentTitle",e)},expression:"addRoleFrom.chatgtpContentTitle"}})],1):t._e(),"2"===t.addRoleFrom.roleAddType?e("el-form-item",{attrs:{label:"自定义提示语",prop:"chatgtpContent"}},[e("v-md-editor",{attrs:{height:"400px"},model:{value:t.addRoleFrom.chatgtpContent,callback:function(e){t.$set(t.addRoleFrom,"chatgtpContent",e)},expression:"addRoleFrom.chatgtpContent"}})],1):t._e(),e("el-form-item",[e("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.onSubmit("addForm")}}},[t._v("提交")])],1)],1)],1)],1),e("div",{staticClass:"options-box"},[e("div",{staticClass:"slider-box"},[e("div",{staticClass:"item"},[e("div",{staticClass:"title"},[t._v(" max_tokens "),e("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"最大输出token数",placement:"top-start"}},[e("i",{staticClass:"el-icon-warning-outline"})])],1),e("div",{staticClass:"slider"},[e("div",{staticClass:"label"},[t._v(t._s(t.reqAiConfig.maxOutputTokens))]),e("el-slider",{attrs:{max:t.modelDetail.maxOutputTokens,step:1},model:{value:t.reqAiConfig.maxOutputTokens,callback:function(e){t.$set(t.reqAiConfig,"maxOutputTokens",e)},expression:"reqAiConfig.maxOutputTokens"}})],1)]),e("div",{staticClass:"item"},[e("div",{staticClass:"title"},[t._v(" temperature "),e("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"采样温度，控制输出的随机性，取值范围是：[0.0, 1.0]",placement:"top-start"}},[e("i",{staticClass:"el-icon-warning-outline"})])],1),e("div",{staticClass:"slider"},[e("div",{staticClass:"label"},[t._v(t._s(t.reqAiConfig.temperature))]),e("el-slider",{attrs:{step:.1,min:0,max:1},model:{value:t.reqAiConfig.temperature,callback:function(e){t.$set(t.reqAiConfig,"temperature",e)},expression:"reqAiConfig.temperature"}})],1)]),e("div",{staticClass:"item"},[e("div",{staticClass:"title"},[t._v(" top_p "),e("el-tooltip",{staticClass:"item",attrs:{effect:"light",content:"温度取样的另一种方法，取值范围是：[0.0, 1.0]",placement:"top-start"}},[e("i",{staticClass:"el-icon-warning-outline"})])],1),e("div",{staticClass:"slider"},[e("div",{staticClass:"label"},[t._v(t._s(t.reqAiConfig.topP))]),e("el-slider",{attrs:{step:.1,min:0,max:1},model:{value:t.reqAiConfig.topP,callback:function(e){t.$set(t.reqAiConfig,"topP",e)},expression:"reqAiConfig.topP"}})],1)])]),e("div",{staticClass:"switch-box"},[e("div",{staticClass:"label"},[t._v("工具调用")]),e("div",{staticClass:"item"},[e("div",{staticClass:"t"},[t._v("网页检索")]),e("div",{staticClass:"switch"},[e("el-switch",{attrs:{activeValue:1,inactiveValue:0,"active-color":"#6126F5","inactive-color":"#DEE0E4",disabled:""},model:{value:t.modelDetail.isNetwork,callback:function(e){t.$set(t.modelDetail,"isNetwork",e)},expression:"modelDetail.isNetwork"}})],1)])]),e("div",{staticClass:"switch-box"},[e("div",{staticClass:"item"},[e("div",{staticClass:"t"},[t._v("多轮对话")]),e("div",{staticClass:"switch"},[e("el-switch",{attrs:{"active-color":"#6126F5","inactive-color":"#DEE0E4"},model:{value:t.reqAiConfig.isMultiple,callback:function(e){t.$set(t.reqAiConfig,"isMultiple",e)},expression:"reqAiConfig.isMultiple"}})],1)])])])])},ca=[],ra={name:"layout-chat-main",components:{ChatBox:xi},async created(){this.models=await N({is_all_pc:1}),this.baseRoles=await q(),this.models.push({name:"通用AI模型",value:""}),this.baseRoles.push({name:"通用对话角色",value:""}),this.getHelpRoleList(),this.getAiVendorList(),document.addEventListener("click",this.handleClickOutside)},props:{_chatDialogs:Array,_chatingId:String,_chatObj:Object},data(){return{models:[],baseRoles:[],roles:[],selectRoleKey:"",selectRole:{id:"",name:"通用对话角色",type:"base"},textarea:"",msg:[],isNewGlobalMsg:!1,msgImgs:[],videos:[],imgInputVisible:!1,videoInputVisible:!1,isPrviewShow:!1,isAddRoleShow:!1,isHelp:!1,addRoleFrom:{isAdd:!0,chatLunciGuid:"",roleAddType:"1",copywritingCategoryId:"",chatgtpContent:"",chatgtpContentTitle:"",historyGuid:""},classList:[],helpList:[],rules:{copywritingCategoryId:[{required:!0,message:"请选择角色",trigger:"change"}],chatgtpContent:[{required:!0,message:"请输入角色名",trigger:"blur"}],chatgtpContentTitle:[{required:!0,message:"请输入角色规则",trigger:"blur"}]},modelPopCofig:{aiPop:!1,modelVendorSign:"",modelType:"text",modelName:"获取中...",guid:"",isFirst:!0,isTabLoading:!1},reqAiConfig:{msgId:"",roleId:"",content_cate_id:"",aiModelGuid:"",roleType:"",chatScene:"default",sceneValue:"",maxOutputTokens:0,temperature:0,topP:0,systemPrompt:"",isMultiple:!0,isSend:!1,placeholder:"请输入您的问题或需求"},modelDetail:{maxOutputTokens:0,modelType:"text",isImageRecognition:0,isVideoRecognition:0},aiModels:[],aiModelChildren:[],source:null,historyHelp:[]}},computed:{msgImgsCount(){return this.msgImgs.length},msgVideoCount(){return this.videos.length}},watch:{_chatingId:{async handler(t,e){t!==e&&this.getHelpRoleList()},immediate:!0}},methods:{async onChangeXzStatus(t){if("end"===t)try{let e=await rt({helpLuinciGuid:this._chatObj.guid,status:t});0==e.code?this.$message({message:"结束协助",type:"success"}):this.$message({message:e.msg,type:"info"})}catch(e){this.$message({message:e.msg,type:"info"})}else try{let e=await rt({helpLuinciGuid:this._chatObj.guid,status:t});0==e.code?this.$message({message:"返回成功",type:"success"}):this.$message({message:e.msg,type:"info"})}catch(e){this.$message({message:e.msg,type:"info"})}},handleClickOutside(t){this.$refs.aiPopRef.contains(t.target)||(this.modelPopCofig.aiPop=!1)},onEditRole(t){let e=null;this.helpList.forEach((i=>{i.sysId===t.id&&(e=i)})),e&&(this.addRoleFrom.roleAddType="2",this.addRoleFrom.chatLunciGuid=e.helpLunciGuid,this.addRoleFrom.guid=e.guid,this.addRoleFrom.chatgtpContent=e.chatgtpContent,this.addRoleFrom.chatgtpContentTitle=e.chatgtpContentTitle,this.addRoleFrom.isAdd=!1,this.isAddRoleShow=!0)},onDeleteRole(t){this.$confirm("是否删除该角色","删除角色",{confirmButtonText:"确认",cancelButtonText:"取消"}).then((async()=>{let e=this.helpList.find((e=>e.sysId===t.id));await et({guid:e.guid}),this.getHelpRoleList(),this.$message({type:"success",message:"删除成功"})})).catch((()=>{this.$message({type:"info",message:"已取消"})}))},onSubmit(t){this.$refs[t].validate((async e=>{if(!e)return!1;if(this.addRoleFrom.isAdd){this.addRoleFrom.chatLunciGuid=this._chatingId;let e=await $(this.addRoleFrom);0===e.code?(this.$nextTick((()=>{this.$refs[t].resetFields()})),this.$message({message:"新增成功",type:"success"}),this.isAddRoleShow=!1,this.getHelpRoleList()):this.$message({message:"新增失败",type:"error"})}else{let e=await tt(this.addRoleFrom);0===e.code?(this.$nextTick((()=>{this.$refs[t].resetFields()})),this.$message({message:"修改成功",type:"success"}),this.isAddRoleShow=!1,this.getHelpRoleList()):this.$message({message:"修改失败",type:"error"})}}))},async getClassList(){let t=await ji({cateType:"text",merchantGuid:this.$store.state.merchantGuid});t.data.forEach((async t=>{let e=await Hi({cateId:t.id});this.classList.push(...e.data)}))},addRolePop(){this.addRoleFrom.isAdd=!0,this.isAddRoleShow=!0,this.historyHelpRoles()},async historyHelpRoles(){let t=await ht();this.historyHelp=t.data},onChangeHistoryRole(t){this.addRoleFrom.chatgtpContentTitle=t.chatgtpContentTitle,this.addRoleFrom.chatgtpContent=t.chatgtpContent},async getHelpRoleList(){let t=await Z({chatLunciGuid:this._chatingId}),e=[];this.helpList=t.data,t.data.forEach((t=>{let i={id:t.sysId,name:t.showRoleName,isHelp:!0,isDelete:t.isDelete,isEdit:t.isEdit};e.push(i)})),this.roles=e},uploadSuccess({code:t,data:e}){0==t&&this.msgImgs.push(e)},beforeRemove({response:t}){this.msgImgs=this.msgImgs.filter((e=>e!=t.data))},handleExceed(){alert("超过文件上传数量")},videoBeforeRemove({response:t}){this.videos=this.videos.filter((e=>e!=t.data))},videoUploadSuccess({code:t,data:e}){0==t&&this.videos.push(e)},choooseRole(t,e){t.isHelp&&(this.isHelp=!0),this.selectRoleKey=t.id,this.selectRole="xz"===e?this.roles.find((e=>e.id==t.id)):this.baseRoles.find((e=>e.id==t.id)),this.selectRole.type=e},onXzRole(){0===this.roles.length&&this.$message({message:"暂无可协助角色",type:"info"})},cleanDialogs(){this.$emit("clearChatDialogs"),z(this._chatingId)},onShowPop(t){"video"===t&&(this.videoInputVisible=!0,0===this.videos.length&&this.$nextTick((()=>{this.$refs.videoRef.clearFiles()}))),"img"===t&&(this.imgInputVisible=!0,0===this.msgImgs.length&&this.$nextTick((()=>{this.$refs.imgRef.clearFiles()})))},async send(){if(1==this._chatObj.helpStatus)return void this.$message({message:"当前协作已完成",type:"warning"});if(!this.reqAiConfig.isSend)return void this.$message({message:"AI模型错误或正在生成中，请稍后重试...",type:"warning"});if(!this.textarea)return;if("image"===this.modelDetail.modelType)return void this.waitingImage();if("video"===this.modelDetail.modelType)return void this.waitingVideo();let t=this.textarea;this.textarea="";let e="",i=this._chatDialogs.length;i>0&&(e=this._chatDialogs[i-1].msgId);let s=await G({role:"user",content:t,lastMsgId:this.reqAiConfig.isMultiple?e:"",aiModelGuid:this.modelPopCofig.guid,chatLunciGuid:this._chatingId,imgs:this.msgImgs,videos:this.videos,isHelp:"1",chatScene:"helpLunciDebug",sceneValue:this._chatObj.guid});return this._chatDialogs.push({msgId:"new",chatRole:"user",chatContent:t,imgUrls:this.msgImgs,videoUrls:this.videos}),this._chatDialogs.push({msgId:"new",chatRole:"assistant",chatContent:"",starting:!0,finished:!1}),this.msgImgs=[],this.videos=[],this.toBottom(),await this.waitChat(s),!1},toBottom(){this.$nextTick((()=>{let t=document.getElementById("chatbox");t.scrollTop=t.scrollHeight}))},async waitChat(t){let e=this._chatDialogs[this._chatDialogs.length-1],i=this,s=`roleId=${this.selectRoleKey}&aiModelGuid=${this.modelPopCofig.guid}&msgId=${t}&roleType=${this.isHelp?"2":"1"}&maxOutputTokens=${this.reqAiConfig.maxOutputTokens}&temperature=${this.reqAiConfig.temperature}&topP=${this.reqAiConfig.topP}`,a=`https://ai-api.deepcity.cn/square/api.chat/sendOpen?${s}`;this.source=new EventSource(a),this.source.onmessage=async function({data:s}){s&&(e.starting=!1),"[DONE]"==s?(e.finished=!0,e.msgId=await G({role:"assistant",content:e.chatContent,aiModelGuid:i.modelPopCofig.guid,lastMsgId:i.reqAiConfig.isMultiple?t:"",chatLunciGuid:i._chatingId,model:i.selectModelKey,chatScene:"helpLunciDebug",sceneValue:i._chatObj.guid}),i.source.close()):e.chatContent+=s.replace(/\\n/g,"\n")},this.source.onopen=function(t){console.log("Connection was opened")},this.source.onerror=async function(t){e.finished=!0,i.source.close()}},onPause(){let t=this._chatDialogs[this._chatDialogs.length-1];this.source.close(),t.finished=!0},async waitingImage(){let t=this.textarea;this.textarea="";let e=await G({role:"user",content:t,lastMsgId:"",contentType:"text",aiModelGuid:this.modelPopCofig.guid,chatLunciGuid:this._chatingId,chatScene:"helpLunciDebug",sceneValue:this._chatObj.guid});this._chatDialogs.push({msgId:"new",chatRole:"user",chatContent:t}),this._chatDialogs.push({msgId:"new",chatRole:"assistant",chatContent:"",starting:!0,finished:!0}),this.toBottom();let i=await j({imgText:t,aiModelGuid:this.modelPopCofig.guid});if(i){let t=this._chatDialogs[this._chatDialogs.length-1];t.starting=!1,t.imgUrls=[i],t.msgId=await G({role:"assistant",content:i,contentType:"img",aiModelGuid:this.modelPopCofig.guid,lastMsgId:this.reqAiConfig.isMultiple?e:"",chatLunciGuid:this._chatingId,chatScene:"helpLunciDebug",sceneValue:this._chatObj.guid})}},async waitingVideo(){let t=this.textarea;this.textarea="";let e=await G({role:"user",content:t,lastMsgId:"",contentType:"text",aiModelGuid:this.modelPopCofig.guid,chatLunciGuid:this._chatingId,chatScene:"helpLunciDebug",sceneValue:this._chatObj.guid});this._chatDialogs.push({msgId:"new",chatRole:"user",chatContent:t}),this._chatDialogs.push({msgId:"new",chatRole:"assistant",chatContent:"",starting:!0,finished:!0}),this.toBottom();let i=await genVideoApi({userPrompt:t,aiModelGuid:this.modelPopCofig.guid});this.$emit("changeOnChatState",!1),0==i.code&&(this.queryGenVideo(i.data.orderNo,e),this.reqAiConfig.isSend=!1)},queryGenVideo(t,e){let i=setInterval((async()=>{let s=await queryGenVideoApi({orderNo:t});if(0==s.code){if("success"===s.data.status){this.reqAiConfig.isSend=!0,this.$emit("changeOnChatState",!0),clearInterval(i);let t=this._chatDialogs[this._chatDialogs.length-1];t.starting=!1,t.videoUrls=[s.data.videoUrl],t.msgId=await G({role:"assistant",content:s.data.videoUrl,contentType:"video",aiModelGuid:this.modelPopCofig.guid,lastMsgId:this.reqAiConfig.isMultiple?e:"",chatLunciGuid:this._chatingId,chatScene:"helpLunciDebug",sceneValue:this._chatObj.guid})}}else s.data.status}),5e3)},async getAiVendorList(){let t=await st();this.aiModels=t.data,t.data.length>0&&(this.modelPopCofig.modelVendorSign=t.data[0].sign,this.getAiVendorModelList())},async getAiVendorModelList(){this.aiModelChildren=[],this.modelPopCofig.isTabLoading=!0;let t=await at(this.modelPopCofig);this.modelPopCofig.isTabLoading=!1,this.aiModelChildren=t.data,t.data.length>0&&this.modelPopCofig.isFirst&&(this.reqAiConfig.isSend=!0,this.modelDetail=t.data[0],this.initChatOptions(t.data[0]),this.modelPopCofig.guid=t.data[0].guid,this.modelPopCofig.modelName=t.data[0].modelName,this.modelPopCofig.isFirst=!1)},onModelBtn(){this.modelPopCofig.aiPop=!this.modelPopCofig.aiPop},onModelChange(t){this.modelPopCofig.modelVendorSign=t.sign,this.getAiVendorModelList()},onModelTypeChange(t){this.modelPopCofig.modelType=t,this.getAiVendorModelList()},onModelChoose(t){this.modelDetail=t,this.initChatOptions(t),this.modelPopCofig.modelName=t.modelName,this.modelPopCofig.guid=t.guid,this.modelPopCofig.aiPop=!1},initChatOptions(t){switch(this.reqAiConfig.maxOutputTokens=Number((t.maxOutputTokens/2).toFixed()),this.reqAiConfig.topP=.6,this.reqAiConfig.temperature=.7,t.modelType){case"text":this.reqAiConfig.placeholder="请输入你的问题和需求";break;case"image":this.reqAiConfig.placeholder="请描述你想生成的图片(生成图片需等待1-2分钟)";break;case"video":this.reqAiConfig.placeholder="请描述你想生成的视频(生成视频需等待较长时间，生成时请耐心等待⌛️)";break;default:break}}},mounted(){this.getClassList()},beforeDestroy(){document.removeEventListener("click",this.handleClickOutside)}},da=ra,ha=(0,wt.A)(da,la,ca,!1,null,"0c756bba",null),ua=ha.exports,pa={name:"layout-tools",components:{LayoutCooperationLeft:na,LayoutCooperationMain:ua},async mounted(){},data(){return{chatList:[],guid:"",chatDialogs:[],onChatState:!0}},methods:{createChat(t){this.guid=t},async helpLunciList(){let t=await Et();t.data.forEach((t=>{null!=t.lunciInfo&&this.chatList.push(t)}));let e=this.$route.query.xzGuid;if(e){let i=t.data.findIndex((t=>t.guid===e));this.guid=-1===i?t.data[0].chatLunciGuid:t.data[i].chatLunciGuid}else t.data.length>0&&(this.guid=t.data[0].chatLunciGuid)},clearChatDialogs(){this.chatDialogs=[]},changeOnChatState(t){this.onChatState=t}},mounted(){this.helpLunciList()},computed:{chatObj(){let t=this.chatList.find((t=>t.chatLunciGuid==this.guid));return t||null}},watch:{async guid(t){this.chatDialogs=[];let e=await U({chatLunciGuid:t});this.$nextTick((()=>{e.forEach((t=>{t.finished=!0,"assistant"===t.chatRole&&("img"==t.contentType&&(t.imgUrls=[t.chatContent],t.chatContent=""),"video"==t.contentType&&(t.videoUrls=[t.chatContent],t.chatContent=""))})),this.chatDialogs=e}))}}},ga=pa,ma=(0,wt.A)(ga,$s,ta,!1,null,null,null),va=ma.exports,Ca=function(){var t=this,e=t._self._c;return e("el-main",{staticClass:"layout-big-vip"},[e("div",{staticClass:"header-box"},[e("img",{staticClass:"icon",attrs:{src:i(8594)}})]),e("div",{staticClass:"container"},[e("div",{staticClass:"now-vip"},[e("div",{staticClass:"vip-info"},[e("p",{staticClass:"card-name"},[t._v(t._s(t.vipInfo.cardName))]),e("div",{staticClass:"vip-notice",domProps:{innerHTML:t._s(t.vipInfo.cardDesc)}}),e("div",{staticClass:"btn"},[t._v("权益生效中")])]),e("div",{staticClass:"config-box"},[e("div",{staticClass:"item"},[e("div",{staticClass:"lable"},[t._v("账号权益享有：")]),e("div",{staticClass:"content"},[t._v("个人")])]),e("div",{staticClass:"item"},[e("div",{staticClass:"lable"},[t._v("权益到期时间：")]),e("div",{staticClass:"content"},[t._v(t._s(t.vipInfo.cardEndTime))])]),e("div",{staticClass:"item"},[e("div",{staticClass:"lable"},[t._v("权益郑重说明：")]),e("div",{staticClass:"content",domProps:{innerHTML:t._s(t.vipInfo.cardNotice)}})])])]),t.bigVipInfo.isBigVip?e("div",{staticClass:"vip-config"},[e("p",{staticClass:"title"},[t._v("我的大会员：")]),e("div",{staticClass:"vip-content"},[e("div",{staticClass:"item"},[e("div",{staticClass:"lable"},[t._v("可提现金额：")]),e("div",{staticClass:"btn-box"},[e("div",{staticClass:"price"},[t._v("￥"+t._s(t.bigVipInfo.bigVipInfo.balance))]),e("div",{staticClass:"sub-btn"},[e("div",{staticClass:"button b1",on:{click:t.onCashOut}},[t._v("提现")]),e("div",{staticClass:"button b2",on:{click:t.ShowTXDialog}},[t._v("提现记录")])])])]),e("div",{staticClass:"item"},[e("div",{staticClass:"lable"},[t._v("助理算力：")]),e("div",{staticClass:"btn-box"},[e("div",{staticClass:"price"},[t._v(t._s(t.bigVipInfo.bigVipInfo.aiPoint))]),e("div",{staticClass:"sub-btn"},[e("div",{staticClass:"button b1",on:{click:t.showPayMeal}},[t._v("采购算力")]),e("div",{staticClass:"button b2",on:{click:t.onShowGivePrint}},[t._v("赠送算力")])])])]),e("div",{staticClass:"item"},[e("div",{staticClass:"lable"},[t._v("累积分佣金额：￥"+t._s(t.bigVipInfo.bigVipInfo.balanceTotal))]),e("div",{staticClass:"btn-box"},[e("div",{staticClass:"price"},[t._v(t._s(t.bigVipInfo.bigVipInfo.balanceTotal))]),e("div",{staticClass:"sub-btn"})])]),e("div",{staticClass:"item"},[e("div",{staticClass:"lable"},[t._v("分佣邀请链接：")]),e("div",{staticClass:"btn-box"},[e("div",{staticClass:"price"},[e("span",{staticClass:"invitationText"},[t._v(t._s(t.invitationPath))])]),e("div",{staticClass:"sub-btn"},[e("div",{staticClass:"button b1",on:{click:t.onCopyIntPath}},[t._v("复制邀请链接")]),e("div",{staticClass:"button b2",on:{click:t.onShowIntDialog}},[t._v("邀请记录")])])])]),e("div",{staticClass:"item"},[e("div",{staticClass:"lable"},[t._v("协作者联盟")]),e("div",{staticClass:"btn-box"},[e("div",{staticClass:"sub-btn"},[t.xzInfo.is_join?e("div",{staticClass:"button c2",on:{click:t.onGoToCenter}},[t._v("我的协作")]):e("div",{staticClass:"button c1",on:{click:t.onGoToCreate}},[t._v("加入协作者联盟")])])])])])]):t._e(),t.bigVipInfo.isBigVip?t._e():e("div",{staticClass:"vip-desc"},[e("p",{staticClass:"title"},[t._v("大会员权益：")]),e("div",{staticClass:"info",domProps:{innerHTML:t._s(t.buyBigVipRuleText)}}),e("div",{staticClass:"btn-box"},[e("div",[e("el-button",{attrs:{type:"primary",disabled:!t.bigVipInfo.isAbleApply},on:{click:t.onShowApplyVip}},[t._v("申请开通大会员")]),e("el-button",{attrs:{type:"info",disabled:!t.bigVipInfo.isAbleApply},on:{click:t.onShowApplyRecord}},[t._v("申请记录")])],1),t.bigVipInfo.isAbleApply?t._e():e("p",{staticClass:"tip"},[t._v("开通年费会员后可申请")])])])]),e("el-dialog",{attrs:{title:"申请大会员",visible:t.applyFormVisible},on:{"update:visible":function(e){t.applyFormVisible=e}}},[e("div",{staticClass:"public-dialog"},[e("div",{staticClass:"header"},[t._v("申请大会员")]),e("div",{staticClass:"content"},[e("el-form",{attrs:{model:t.applyForm}},[e("el-form-item",{attrs:{label:"申请备注","label-width":"80px"}},[e("el-input",{attrs:{autocomplete:"off"},model:{value:t.applyForm.applyRemark,callback:function(e){t.$set(t.applyForm,"applyRemark",e)},expression:"applyForm.applyRemark"}})],1)],1)],1),e("div",{staticClass:"btn-box"},[e("div",{staticClass:"btn two",on:{click:t.onApply}},[t._v("申 请")]),e("div",{staticClass:"btn two b2",on:{click:function(e){t.applyFormVisible=!1}}},[t._v("取消")])])])]),e("el-dialog",{attrs:{title:"申请记录",visible:t.applyListVisible},on:{"update:visible":function(e){t.applyListVisible=e}}},[e("div",{staticClass:"public-dialog"},[e("div",{staticClass:"header"},[t._v("申请记录")]),e("div",{staticClass:"content"},[e("el-table",{attrs:{data:t.applyList}},[e("el-table-column",{attrs:{property:"applyRemark","show-overflow-tooltip":!0,label:"申请备注",width:"200"}}),e("el-table-column",{attrs:{property:"refuseReason",label:"拒绝理由",width:"200"}}),e("el-table-column",{attrs:{property:"applyStatusText",label:"申请状态"}})],1)],1)])]),e("el-dialog",{attrs:{title:"提现申请记录",visible:t.txApplyListVisible,width:"600px"},on:{"update:visible":function(e){t.txApplyListVisible=e}}},[e("div",{staticClass:"public-dialog"},[e("div",{staticClass:"header"},[t._v("提现申请记录")]),e("div",{staticClass:"content"},[e("el-table",{attrs:{data:t.txApplyList}},[e("el-table-column",{attrs:{property:"withdrawAmount",label:"提现金额"}}),e("el-table-column",{attrs:{property:"withdrawStatusText",label:"提现状态",width:"100"}}),e("el-table-column",{attrs:{property:"payVoucherImg",label:"提现凭证",width:"120"},scopedSlots:t._u([{key:"default",fn:function(t){return[e("el-image",{staticStyle:{width:"80px",height:"80px"},attrs:{src:t.row.payVoucherImg,"preview-src-list":[t.row.payVoucherImg]}})]}}])}),e("el-table-column",{attrs:{property:"withdrawTime",label:"提现时间"}})],1)],1)])]),e("el-dialog",{attrs:{title:"邀请记录",visible:t.invitationListVisible,width:"600px"},on:{"update:visible":function(e){t.invitationListVisible=e}}},[e("div",{staticClass:"public-dialog"},[e("div",{staticClass:"header"},[t._v("邀请记录")]),e("div",{staticClass:"content"},[e("el-table",{attrs:{data:t.invitationList}},[e("el-table-column",{attrs:{property:"inviteUser.headImgurl",label:"头像",width:"80"},scopedSlots:t._u([{key:"default",fn:function(t){return[e("el-image",{staticStyle:{width:"30px",height:"30px"},attrs:{src:t.row.inviteUser.headImgurl,"preview-src-list":[t.row.inviteUser.headImgurl]}})]}}])}),e("el-table-column",{attrs:{property:"inviteUser.nickname",label:"用户名"}}),e("el-table-column",{attrs:{property:"createTime",label:"邀请时间"}})],1),e("el-pagination",{staticClass:"mt20",attrs:{small:"",background:"",layout:"prev, pager, next",total:t.invitationTotal,"current-page":t.inviteReq.page},on:{"current-change":t.handlePageChang}})],1)])]),e("el-dialog",{attrs:{title:"新增提现账号",visible:t.addCashOutVisible,width:"600px"},on:{"update:visible":function(e){t.addCashOutVisible=e}}},[e("div",{staticClass:"public-dialog"},[e("div",{staticClass:"header"},[t._v("新增提现账号")]),e("div",{staticClass:"content"},[e("el-tabs",{model:{value:t.addCashOutForm.withdrawMethod,callback:function(e){t.$set(t.addCashOutForm,"withdrawMethod",e)},expression:"addCashOutForm.withdrawMethod"}},[e("el-tab-pane",{attrs:{label:"银行卡",name:"3"}},[e("el-input",{attrs:{size:"medium",placeholder:"请输入银行卡账号"},model:{value:t.addCashOutForm.withdrawAccount,callback:function(e){t.$set(t.addCashOutForm,"withdrawAccount",e)},expression:"addCashOutForm.withdrawAccount"}}),e("div",{staticClass:"mt10"},[e("el-input",{attrs:{size:"medium",placeholder:"请输入收款人姓名"},model:{value:t.addCashOutForm.withdrawName,callback:function(e){t.$set(t.addCashOutForm,"withdrawName",e)},expression:"addCashOutForm.withdrawName"}})],1),e("div",{staticClass:"mt10"},[e("el-input",{attrs:{size:"medium",placeholder:"收款银行"},model:{value:t.addCashOutForm.bankName,callback:function(e){t.$set(t.addCashOutForm,"bankName",e)},expression:"addCashOutForm.bankName"}})],1),e("div",{staticClass:"mt10"},[e("el-input",{attrs:{size:"medium",placeholder:"请输入收款银行支行"},model:{value:t.addCashOutForm.bankBranchName,callback:function(e){t.$set(t.addCashOutForm,"bankBranchName",e)},expression:"addCashOutForm.bankBranchName"}})],1)],1)],1)],1),e("div",{staticClass:"btn-box"},[e("div",{staticClass:"btn one",on:{click:t.onSubmitPayMode}},[t._v("提 交")])])])]),e("el-dialog",{attrs:{title:"提现列表",visible:t.cashOutListVisible,width:"700px"},on:{"update:visible":function(e){t.cashOutListVisible=e}}},[e("div",{staticClass:"public-dialog"},[e("div",{staticClass:"header"},[t._v("提现列表")]),e("div",{staticClass:"content"},[e("el-table",{attrs:{data:t.accountList,"empty-text":"请新增提现账号"}},[e("el-table-column",{attrs:{property:"withdrawMethod",label:"提现方式",width:"100"},scopedSlots:t._u([{key:"default",fn:function(i){return[1==i.row.withdrawMethod?e("div",[t._v("微信提现")]):t._e(),2==i.row.withdrawMethod?e("div",[t._v("支付宝提现")]):t._e(),3==i.row.withdrawMethod?e("div",[t._v("银行卡提现")]):t._e()]}}])}),e("el-table-column",{attrs:{property:"withdrawAccount",label:"账号",width:"150"}}),e("el-table-column",{attrs:{property:"withdrawName",label:"收款人",width:"150"}}),e("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(i){return[e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.onDeleteMode(i.row)}}},[t._v(" 删 除 ")]),e("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.onWithdrawal(i.row.guid)}}},[t._v("使用该账号提现")])]}}])})],1)],1),e("div",{staticClass:"btn-box"},[e("div",{staticClass:"btn one",on:{click:t.onShowAddDialog}},[t._v("新增提现账号")])])])]),e("el-dialog",{attrs:{title:"发起提现申请",visible:t.cashOutVisible,width:"600px"},on:{"update:visible":function(e){t.cashOutVisible=e}}},[e("div",{staticClass:"public-dialog"},[e("div",{staticClass:"header"},[t._v("发起提现申请")]),e("div",{staticClass:"content"},[e("el-form",{ref:"form",attrs:{model:t.cashOutForm,"label-width":"80px"}},[e("el-form-item",{attrs:{label:"提现金额"}},[e("el-input-number",{attrs:{"controls-position":"right",step:t.cashOutStep,"step-strictly":"",min:t.cashOutStep},model:{value:t.cashOutForm.withdraw_amount,callback:function(e){t.$set(t.cashOutForm,"withdraw_amount",e)},expression:"cashOutForm.withdraw_amount"}}),e("span",[t._v(" (请输入"+t._s(t.cashOutStep)+"的倍数)可提现金额：￥"+t._s(t.bigVipInfo.bigVipInfo.balance))])],1),e("el-form-item",{attrs:{label:"备注"}},[e("el-input",{attrs:{size:"medium",placeholder:"请输入备注（选填）"},model:{value:t.cashOutForm.withdraw_remark,callback:function(e){t.$set(t.cashOutForm,"withdraw_remark",e)},expression:"cashOutForm.withdraw_remark"}})],1)],1)],1),e("div",{staticClass:"btn-box"},[e("div",{staticClass:"btn one",on:{click:t.onAddCashSubmit}},[t._v("提交")])])])]),e("el-dialog",{attrs:{title:"赠送算力",visible:t.showGiveVisible,width:"600px"},on:{"update:visible":function(e){t.showGiveVisible=e}}},[e("div",{staticClass:"public-dialog"},[e("div",{staticClass:"header"},[t._v("发起提现申请")]),e("div",{staticClass:"content"},[e("el-form",{ref:"form",attrs:{model:t.givePrintForm,"label-width":"80px"}},[e("el-form-item",{attrs:{label:"用户名"}},[e("el-input",{attrs:{size:"medium",placeholder:"请输入用户名"},model:{value:t.givePrintForm.keywords,callback:function(e){t.$set(t.givePrintForm,"keywords",e)},expression:"givePrintForm.keywords"}},[e("el-button",{attrs:{slot:"append",icon:"el-icon-search"},on:{click:t.onSeachUser},slot:"append"})],1)],1),e("el-form-item",{attrs:{label:"赠送算力"}},[e("el-input-number",{attrs:{"controls-position":"right",min:1,max:t.bigVipInfo.bigVipInfo.aiPoint},on:{change:t.onPrintchange},model:{value:t.givePrintForm.aiPoint,callback:function(e){t.$set(t.givePrintForm,"aiPoint",e)},expression:"givePrintForm.aiPoint"}})],1),e("el-form-item",{attrs:{label:"选择用户"}},[t.giveUsers.length>0?e("div",t._l(t.giveUsers,(function(i){return e("el-radio",{key:i.guid,attrs:{label:i.guid},model:{value:t.givePrintForm.giveUserGuid,callback:function(e){t.$set(t.givePrintForm,"giveUserGuid",e)},expression:"givePrintForm.giveUserGuid"}},[t._v(t._s(i.nickname))])})),1):e("p",[t._v("请搜索想要赠送的用户")])])],1)],1),e("div",{staticClass:"btn-box"},[e("div",{staticClass:"btn two",on:{click:t.onGiveSubmit}},[t._v("赠 送")]),e("div",{staticClass:"btn two b2",on:{click:t.onGiveHistory}},[t._v("记 录")])])])]),e("el-dialog",{attrs:{title:"购买算力",visible:t.showBuyVisible,width:"600px"},on:{"update:visible":function(e){t.showBuyVisible=e}}},[e("div",{staticClass:"public-dialog"},[e("div",{staticClass:"header"},[t._v("购买算力")]),e("div",{staticClass:"content"},[e("el-form",{ref:"form",attrs:{model:t.givePrintForm,"label-width":"80px"}},[e("el-form-item",{attrs:{label:"购买算力"}},[e("el-input-number",{attrs:{"controls-position":"right",min:1,max:parseFloat(t.bigVipInfo.bigVipInfo.balance)},on:{change:t.onBuyPrintchange},model:{value:t.bugPrintFrom.exchangeAmount,callback:function(e){t.$set(t.bugPrintFrom,"exchangeAmount",e)},expression:"bugPrintFrom.exchangeAmount"}}),t._v(" 元 = "+t._s(t.bugPrintFrom.exchangeAmount*t.buyPointStep)+"点算力 ")],1),e("el-form-item",{attrs:{label:"会员价:"}},[t._v(" 当前大会员1元可兑换"+t._s(t.buyPointStep)+"点算力 ")])],1)],1),e("div",{staticClass:"btn-box"},[e("div",{staticClass:"btn one",on:{click:t.onBuySubmit}},[t._v("购 买")])])])]),e("el-dialog",{attrs:{title:"赠送记录",visible:t.giveHistoryVisible,width:"600px"},on:{"update:visible":function(e){t.giveHistoryVisible=e}}},[e("div",{staticClass:"public-dialog"},[e("div",{staticClass:"header"},[t._v("赠送记录")]),e("div",{staticClass:"content"},[e("el-table",{attrs:{data:t.giveHistory,"empty-text":"暂无赠送记录"}},[e("el-table-column",{attrs:{property:"acceptUser.nickname",label:"用户名"}}),e("el-table-column",{attrs:{property:"sendAiPoint",label:"赠送算力",width:"100"}}),e("el-table-column",{attrs:{property:"sendTime",label:"赠送时间"}})],1)],1)])]),e("pay-meal-dialog",{ref:"paymeal"}),e("div",{staticClass:"person"},[e("img",{staticClass:"img",attrs:{src:i(7302)}})])],1)},Aa=[],fa={components:{PayMealDialog:pe},data(){return{vipInfo:{cardName:"",cardDesc:"",cardEndTime:"",cardNotice:""},bigVipInfo:{isBigVip:!1,isAbleApply:!1,bigVipInfo:{balance:"",aiPoint:"",balanceTotal:""}},applyFormVisible:!1,applyListVisible:!1,txApplyListVisible:!1,invitationListVisible:!1,txApplyList:[],invitationList:[],invitationTotal:0,inviteReq:{pageSize:10,page:1},applyForm:{applyRemark:""},applyList:[],addCashOutVisible:!1,cashOutListVisible:!1,cashOutVisible:!1,showGiveVisible:!1,giveHistoryVisible:!1,showBuyVisible:!1,addCashOutForm:{withdrawMethod:"3",withdrawAccount:"",withdrawName:"",bankName:"",bankBranchName:""},cashOutForm:{withdraw_method_guid:"",withdraw_amount:1,withdraw_remark:""},givePrintForm:{keywords:"",giveUserGuid:"",aiPoint:1},bugPrintFrom:{exchangeAmount:1},giveUsers:[],giveHistory:[],accountList:[],invitationPath:"",cashOutStep:0,buyPointStep:0,buyBigVipRuleText:"",xzInfo:{is_join:!1}}},mounted(){this.getUserVipInfo(),this.getUserBigVipInfo(),this.getBigVipConfig(),this.onQueryIsCreate()},methods:{getMerchantInfo(t,e,i=""){const s=t.find((t=>t.key===e));return s?s.value:i},async getBigVipConfig(){let t=await te();this.cashOutStep=parseInt(this.getMerchantInfo(t.data,"big_vip_withdraw_amount_limit",1)),this.buyPointStep=parseInt(this.getMerchantInfo(t.data,"big_vip_one_yuan_exchange_ai_point",1)),this.buyBigVipRuleText=this.getMerchantInfo(t.data,"big_vip_apply_notice_text","")},async getUserVipInfo(){let t=await qt();this.vipInfo.cardDesc=t.data.cardInfo.cardDesc,this.vipInfo.cardName=t.data.cardInfo.cardName,this.vipInfo.cardEndTime=t.data.vipInfo.cardEndTime,this.vipInfo.cardNotice=t.data.cardInfo.cardNotice},async getUserBigVipInfo(){let t=await zt();if(0===t.code){this.bigVipInfo=t.data;let e=window.location.origin;this.invitationPath=`${e}/?inviteCode=${t.data.bigVipInfo.inviteCode}&type=bigVip`}else this.invitationPath="邀请链接生成失败"},onShowApplyVip(){this.applyFormVisible=!0},async onShowApplyRecord(){let t=await Ht();this.applyList=t.data,this.applyListVisible=!0},async ShowTXDialog(){let t=await Zt();this.txApplyList=t.data,this.txApplyListVisible=!0},async onShowIntDialog(){this.bigVipInviteList(),this.invitationListVisible=!0},async bigVipInviteList(){let t=await $t(this.inviteReq);this.invitationList=t.data.data},async handlePageChang(t){this.inviteReq.page=t,bigVipInviteList()},async onApply(){let t=await jt({applyRemark:this.applyForm.applyRemark});0===t.code?(this.$message({message:"申请成功",type:"success"}),this.applyFormVisible=!1):this.$message({message:t.msg,type:"info"})},onCashOut(){this.cashOutListVisible=!0,this.getAccountList()},handleClick(){},async getAccountList(){let t=await Xt();this.accountList=t.data},async onSubmitPayMode(){1==this.addCashOutForm.withdrawMethod||2==this.addCashOutForm.withdrawMethod?this.addCashOutForm.withdrawAccount.trim().length>0&&this.addCashOutForm.withdrawName.trim().length>0?(await Qt(this.addCashOutForm),this.getAccountList(),this.addCashOutVisible=!1,this.cashOutListVisible=!0,this.$message({message:"新增成功",type:"success"})):this.$message({message:"请完善账号信息",type:"info"}):3==this.addCashOutForm.withdrawMethod&&(this.addCashOutForm.withdrawAccount.trim().length>0&&this.addCashOutForm.withdrawName.trim().length>0&&this.addCashOutForm.bankName.trim().length>0&&this.addCashOutForm.bankBranchName.trim().length>0?(await Qt(this.addCashOutForm),this.getAccountList(),this.addCashOutVisible=!1,this.cashOutListVisible=!0,this.$message({message:"新增成功",type:"success"})):this.$message({message:"请完善账号信息",type:"info"}))},clearYH(){this.addCashOutForm.bankName="",this.addCashOutForm.bankBranchName=""},async onWithdrawal(t){this.cashOutForm.withdraw_method_guid=t,this.cashOutVisible=!0},async onDeleteMode(t){await Yt({guid:t.guid}),this.$message({message:"删除成功",type:"success"}),this.getAccountList()},onShowAddDialog(){this.cashOutListVisible=!1,this.addCashOutVisible=!0},showPayMeal(){this.showBuyVisible=!0},async onBuySubmit(){let t=await ee(this.bugPrintFrom);0===t.code?(this.$message({message:"购买成功",type:"success"}),this.showBuyVisible=!1,this.getUserBigVipInfo()):this.$message({message:t.msg,type:"info"})},onPrintchange(t){this.givePrintForm.aiPoint=t.toFixed(0)},onBuyPrintchange(t){this.bugPrintFrom.exchangeAmount=t.toFixed(0)},async onAddCashSubmit(){try{let t=await _t(this.cashOutForm);0===t.code?(this.$message({message:"提现申请成功",type:"success"}),this.cashOutVisible=!1):this.$message({message:t.msg,type:"info"})}catch(t){}},onShowGivePrint(){this.showGiveVisible=!0},async onSeachUser(){if(!this.givePrintForm.keywords)return;let t=await Jt({keywords:this.givePrintForm.keywords});this.giveUsers=t.data},async onGiveSubmit(){if(!this.givePrintForm.giveUserGuid)return;let t=await Wt(this.givePrintForm);0===t.code?(this.$message({message:"赠送成功",type:"success"}),this.showGiveVisible=!1):this.$message({message:t.msg,type:"info"})},async onGiveHistory(){this.giveHistoryVisible=!0;let t=await Kt();this.giveHistory=t.data},onCopyIntPath(){this.$copyText(this.invitationPath),this.$message({message:"已复制邀请链接",type:"success"})},async onQueryIsCreate(){let t=await se();this.xzInfo.is_join=t.data.is_join},onGoToCreate(){this.$router.push("/app/xzcreate")},onGoToCenter(){this.$router.push("/app/xzcenter")}}},ya=fa,ba=(0,wt.A)(ya,Ca,Aa,!1,null,"ecd85c20",null),wa=ba.exports,Ia=function(){var t=this,e=t._self._c;return e("el-main",{staticClass:"layout-create-center"},[e("div",{staticClass:"header-box"},[e("img",{staticClass:"icon",attrs:{src:i(1995)}})]),e("div",{staticClass:"container"},[e("div",{staticClass:"label-box"},[t._v(" 欢迎加入协作者联盟！我们是一个致力于通过协作、共享AI能力来实现创造力与收益的联盟。我们为您提供了一个平台，让您展示您的独特能力并与其他协作者合作，为用户提供出色的AI对话场景体验。"),e("br"),t._v(" 作为联盟成员，您将有机会获得用户选择您作为协作对象的机会。当用户需要协作时，他们将能看到您的个人资料和技能。您可以帮助用户完成AI对话的提示词编写，提供其他AI能力辅助，以满足用户的需求。"),e("br"),t._v(" 在每次成功协作结束后，我们将奖励您协作过程中产生算力点数的50%作为回报，作为对您付出和贡献的认可。"),e("br"),t._v(" 我们鼓励协作者们在协作者联盟平台上展现创造力，与其他成员协作，并取得共同成功。通过共享您的AI能力和经验，您不仅可以为用户提供优质的服务，还能在联盟中建立更多有意义的联系和合作机会。"),e("br"),t._v(" 请注意，我们鼓励和推崇合作、创新、共享的价值观。我们禁止任何形式的侵权、盗用他人作品和不合法行为。请确保您的作品和行为符合相关法律法规和道德准则。我们将重视知识产权保护，保护协作者的权益。"),e("br"),t._v(" 协作者联盟是一个充满活力和创造力的社区，我们相信您的加入将为联盟带来新的活力和价值。让我们共同努力，通过协作，为用户创造出精彩纷呈的AI对话场景体验！ ")]),e("div",{staticClass:"form-box"},[e("div",{staticClass:"title-box"},[t._v("请填写信息，加入联盟")]),e("div",{staticClass:"content"},[e("div",{staticClass:"logo"},[e("el-upload",{staticClass:"avatar-uploader",attrs:{action:"https://ai-api.deepcity.cn/user/api.userinfo/uploadImg","show-file-list":!1,name:"img","on-success":t.handleAvatarSuccess,"before-upload":t.beforeAvatarUpload}},[e("div",{staticClass:"relative"},[e("el-image",{staticClass:"header-img",attrs:{title:"点击上传头像",src:t.applyForm.avatarUrl}}),e("div",{directives:[{name:"show",rawName:"v-show",value:t.uploading,expression:"uploading"}],staticClass:"uploading flex-center"},[e("i",{staticClass:"spin el-icon-loading"})]),e("div",{staticClass:"btn-edit",attrs:{title:"点击上传头像"}},[e("img",{attrs:{src:i(3544)}})])],1)])],1),e("div",{staticClass:"form"},[e("el-form",{attrs:{model:t.applyForm}},[e("el-form-item",{attrs:{label:"昵称","label-width":"80px"}},[e("el-input",{staticClass:"inp",attrs:{placeholder:"请输入名称（必填）",autocomplete:"off"},model:{value:t.applyForm.nickname,callback:function(e){t.$set(t.applyForm,"nickname",e)},expression:"applyForm.nickname"}})],1),e("el-form-item",{attrs:{label:"优势介绍","label-width":"80px"}},[e("el-input",{staticClass:"inp",attrs:{type:"textarea",row:"2",resize:"none",placeholder:"请输入优势介绍",autocomplete:"off"},model:{value:t.applyForm.introduction,callback:function(e){t.$set(t.applyForm,"introduction",e)},expression:"applyForm.introduction"}})],1)],1),e("div",{staticClass:"btn-box"},[e("div",{staticClass:"btn",on:{click:t.onJoin}},[t._v("立即加入")])])],1)])])])])},xa=[],ka={data(){return{applyForm:{avatarUrl:"https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/d1511a2c95cb4f108f39499c5475cbf1.png",nickname:"",introduction:""},uploading:!1}},methods:{handleAvatarSuccess({code:t,data:e}){0==t&&(this.applyForm.avatarUrl=e),this.uploading=!1},beforeAvatarUpload(){this.uploading=!0},async onJoin(){if(""!==this.applyForm.nickname.trim())try{await ae(this.applyForm),this.$message({message:"申请成功",type:"success"}),this.$router.go(-1)}catch(t){this.$message({message:t.msg?t.msg:"服务器错误",type:"error"})}else this.$message({message:"请输入昵称",type:"warning"})}}},Ra=ka,Pa=(0,wt.A)(Ra,Ia,xa,!1,null,"60295abf",null),Ma=Pa.exports,La=function(){var t=this,e=t._self._c;return e("el-main",{staticClass:"layout-create-center"},[e("div",{staticClass:"container"},[e("div",{staticClass:"logo-box"},[e("img",{staticClass:"logo",attrs:{src:t.userInfo.avatarUrl}}),e("span",{staticClass:"name"},[t._v(t._s(t.userInfo.nickname)+"欢迎回来~")])]),e("div",{staticClass:"content-box"},[e("div",{staticClass:"card-box"},[e("div",{staticClass:"item",style:{backgroundImage:"url("+t.bg1+")",color:"#1B0C6E"}},[e("div",{staticClass:"title"},[t._v("累计调试次数")]),e("div",{staticClass:"content"},[e("span",{staticClass:"number"},[t._v(t._s(t.lunciData.totalChatCount))]),t._v(" "),e("span",{staticClass:"unit"},[t._v("次")])])]),e("div",{staticClass:"item",style:{backgroundImage:"url("+t.bg2+")",color:"#0C296E"}},[e("div",{staticClass:"title"},[t._v("协作中聊天轮次")]),e("div",{staticClass:"content"},[e("span",{staticClass:"number"},[t._v(t._s(t.lunciData.doingHelpCount))]),t._v(" "),e("span",{staticClass:"unit"},[t._v("次")])])]),e("div",{staticClass:"item",style:{backgroundImage:"url("+t.bg3+")",color:"#004761"}},[e("div",{staticClass:"title"},[t._v("协作完成聊天轮次")]),e("div",{staticClass:"content"},[e("span",{staticClass:"number"},[t._v(t._s(t.lunciData.finishHelpCount))]),t._v(" "),e("span",{staticClass:"unit"},[t._v("次")])])]),e("div",{staticClass:"item",style:{backgroundImage:"url("+t.bg4+")",color:"#613000"}},[e("div",{staticClass:"title"},[t._v("累计协作收益")]),e("div",{staticClass:"content"},[e("span",{staticClass:"number"},[t._v(t._s(t.lunciData.totalRewardPoints))]),t._v(" "),e("span",{staticClass:"unit"},[t._v("点")])])])]),e("div",{staticClass:"tab-box"},[e("div",{staticClass:"tab"},[e("div",{staticClass:"item",class:{active:"doing"===t.tabActive},on:{click:function(e){return t.onTab("doing")}}},[t._v(" 协作中 ")]),e("div",{staticClass:"item",class:{active:"finish"===t.tabActive},on:{click:function(e){return t.onTab("finish")}}},[t._v(" 协作完成 ")])]),e("div",{staticClass:"content"},[e("el-table",{attrs:{data:t.chatList,"empty-text":"暂无轮次信息"}},[e("el-table-column",{attrs:{property:"lunciInfo.chatLunciTitle",label:"协作轮次名称"},scopedSlots:t._u([{key:"default",fn:function(i){return[null===i.row.lunciInfo?e("div",{staticClass:"nameT"},[t._v("对方聊天轮次已删除")]):e("div",[t._v(t._s(i.row.lunciInfo.chatLunciTitle))])]}}])}),e("el-table-column",{attrs:{property:"lunciUser.nickname",label:"请求协作用户"}}),e("el-table-column",{attrs:{property:"helpStatus",label:"协作状态"},scopedSlots:t._u([{key:"default",fn:function(i){return[0==i.row.helpStatus?e("div",{staticClass:"stateT"},[t._v("协作中")]):t._e(),1==i.row.helpStatus?e("div",{staticClass:"stateT"},[t._v("协作完成")]):t._e()]}}])}),e("el-table-column",{attrs:{property:"helpRewardPoints",label:"当前轮次可获得点数"}}),e("el-table-column",{attrs:{property:"debugChatCount",label:"累计调试点数"}}),e("el-table-column",{attrs:{fixed:"right",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(i){return[0==i.row.helpStatus?e("div",{staticClass:"btn",attrs:{size:"small"},on:{click:function(e){return t.onGotoCooperation(i.row.guid)}}},[t._v(" 前往协作 ")]):t._e()]}}])})],1)],1)])])])])},Sa=[],Da=i.p+"img/center-bg1.739b5d8e.png",Ta=i.p+"img/center-bg2.a83e306e.png",Ea="data:image/png;base64,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",Ba="data:image/png;base64,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**************************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",Oa={data(){return{bg1:Da,bg2:Ta,bg3:Ea,bg4:Ba,lunciData:{totalChatCount:0,waitHelpCount:0,finishHelpCount:0,totalRewardPoints:0,doingHelpCount:0},userInfo:{},chatList:[],chatDoing:[],chatWait:[],chatFinish:[],tabActive:"doing"}},methods:{onGotoCooperation(t){this.$router.push("/app/cooperation?xzGuid="+t)},async helpUserOverview(){let t=await oe();this.lunciData={...this.lunciData,...t.data}},async helpUserInfo(){let t=await se();this.userInfo=t.data.info},async helpLunciList(){let t=await Et();t.data.forEach((t=>{0===t.helpStatus?this.chatDoing.push(t):1===t.helpStatus&&this.chatFinish.push(t)})),this.chatList=this.chatDoing},onTab(t){this.tabActive=t,"doing"===t&&(this.chatList=this.chatDoing),"finish"===t&&(this.chatList=this.chatFinish)}},mounted(){this.helpUserInfo(),this.helpUserOverview(),this.helpLunciList()}},Ua=Oa,Va=(0,wt.A)(Ua,La,Sa,!1,null,"3fade3cd",null),Ga=Va.exports;const Fa=Zs.A.prototype.push;Zs.A.prototype.push=function(t){return Fa.call(this,t).catch((t=>t))},s["default"].use(Zs.A);const Na=new Zs.A({base:"/",mode:"history",routes:[{path:"/",name:"home",component:Ss},{path:"/app",name:"app",component:Is,children:[{path:"chat",name:"chat",component:Ti},{path:"draw",name:"draw",component:ci},{path:"gallery",name:"gallery",component:Gi},{path:"tools",name:"tools",component:fs},{path:"cooperation",name:"cooperation",component:va},{path:"bigvip",name:"bigvip",component:wa},{path:"xzcreate",name:"xzcreate",component:Ma},{path:"xzcenter",name:"xzcenter",component:Ga}]}]});Na.beforeEach((async(t,e,i)=>{const s=t.query.yqGuid;s&&localStorage.setItem("yqGuid",s);let a=C();a?"home"==t.name?i("/app/chat"):i():"home"!=t.name?i("/"):i()})),Na.afterEach((()=>{}));var qa=Na,za=i(3851);Ys().use(Js(),{Hljs:Ks()}),s["default"].use(Xs()),s["default"].use(Ys()),s["default"].use(js()),s["default"].use(u(),{size:"small"}),s["default"].use(qs.A),s["default"].prototype.$store=g,s["default"].config.productionTip=!1,s["default"].component(Ns.name,Ns),s["default"].use(Zs.A),s["default"].use(za.A),new s["default"]({router:qa,store:g,render:t=>t(Os)}).$mount("#app")},2602:function(t){t.exports="data:image/png;base64,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"},8839:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IArs4c6QAABRhJREFUSEvN1ltsVEUYB/D/zLn17LbdS7dX2tJWqJCaNKSVS1EJoiFeSTBoomIimtSER42+iFZ91vhE4Mn4oBBKQqIh+iAh0qCWlhKlAW1aetnedrd0d7uXs+cy85kNSoS2JGBCmLeTmeQ3X+bM/xuG+zTYfXLw4EBnz5JqrIPW1cCs/1P9HSsiopr4ovxCEDrSea9nY6Nx7F6xVSEi0sjGiZRFe7gCphlykQnnGZ/Pd+FesNUhh7pJ4vCSRVw1GFSDSNjuoOtYu4PBYPJusRUhSlJQ+jBEgppzeQIzGRgngCQpko6bfu0tAIXxwaVHXcPYXNDpdPsGc7yIH+yj1vlEYW9TXp75/HX/wL8bWgYREXMKOKQp+NgpEC9YEtAlPClADGBcIQ3asdxYbJB034dCN0IC/K94ytl/9ALqtBr+ZaXkTbvW8v6dW7VtK0Jnx8QHlvR27KjRugydBeychOtISMWF7QnYUACuwixRCQUhJ2YdxbWBHHEaWJJJpii+BpuVJEtUbK/wbFPTy9vamFPEblZUrOTKiLVgaAg31JVAZQxWVsJzPXiwkfcIealCkIbmWhWpNDC+6OHymI2xBYm9m/1wbCCRkJgsVdBlWkIY/tBjG1jmFqj4MXlm+kShMbIvFGTQNQZVMgiHkHGycEhBOq/h1Pk03nutCnmX449ZF7PXCcNxD8826hBSIrHEMV3B0YFMZrR/tKK7u9NdBqX64/viofITWdtDTYWBslKAbMLIdBI1dUF8dyaJNZUGdj0eQDzPcHXOQyIpcSnuYleVAtuRuO7qyNVzbKXCye2P+PeteEbXf5uuz5QHx/LlJTplc4iE/TB1idm4BUkGentn0f3OGuShwfKAkZiHdJYwELPxRIWK2JyDdJkfkVaFOhx7/+Y285sVoegvUTNO2kUnEtxY7WcoLNkIBUy4YPj5hzh0H8OTL1RhaArwGRJTiwKeA1xM2NgUVjE1YmOhuhwdbSz/8JLTtm2TObEi1NNDfEtn9BivCbwcKC1BnekhnxFwHAW9R0bxyvutsDQdIzGCxlxkbEATEsNzBTSEdKRjwNWIiT0b0F8/P7ezq6vhZj4uu0e9pyYOuT7j03BVEM3lKhQvi3PHY8j7GZ569SH8GZOYTRGIXBgqh+EKjEzm4C81ILmBoaCBN1pw+MUW5eB/02MZ9FXv2F6H+MmKxkpmuBw72nVc6k+A15ajAAXXEhJL+WJKeKgNqqCci/GoBUfXwcJl+L1MoY+a+Juda9nXd4SOHL/WXsg550vXVvldV8PWBhXNTRqGogKjExbiS4DfYDA0hrqwimyqgIl5BzFPRT7kR7pOtz6JiJ1trXr/HaFvf8pWR4fHfg2vizSToqOaczQ3aOgbySJk6iBFga5xqDZB1xlSCzYmJyykTQPTio7KLeWzzwl329Od5tQdoaNHB7WW+lDfompsCfhUtLeUwS3epSELpq6iplZFWViH5wrMJxzM2AIwGUjnOBcVNJ4Up3m08qXenhvRs2qoFieGB+d+HJxRdleXCTSGS0CSYWbKBucMlUENsbSDrCXAFQ6PM9gqQ0oAAwWdrpUq3/c9X7bn9jayYpuIXsm9beW9dy+nREvCkWpAEFMFgUsPghjNpDwUXMlMU0GpocJyZDFBxBqFJtNQPjtwoPaWH2FZBN2+CyLS5+ezgfQc1+bmSCZtkrFYDtnsjZURH+fr6xkPr/dRJOLLVFWxf2aWt8UH5xV0ty17tfV/AwUbbjkSiyslAAAAAElFTkSuQmCC"},9359:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAA6tJREFUOE+t1H1ME2cYAPD3uSu968e1R5EPayCBgBoEYQX2kRkNcSMbiCOZKwZHwwzGuY/ISupiFIrOLGG6EWVMYlDG5tCxJURd/cJEwY1MynBWYIurHc6EOUwGvV49uLb3Ltdps/ExIdn7773P733e53nuBfQ/L5jLwxizAzfFItdgcA0v4CSEEWg0MJK1QtFjylQ6AGBittgZIMaY/KZL2N7dG9j9iycQjTFCOi0RjuV4CQEglJaiGM9/ln6v8DnqIABI/4T/BXb0YtXYzw++Pn9JKDQmkEJWhuLMU7nKU4yWuCsH+Xgp0TkQeOm6Sywe/UNSvbiWPherHX/ZbE4UHqERUM6s6eiD045LQmGeSencuI7anJ5ODc52reHhqYyTp6eOOX8U8wrX0mffqtSsB4CQvDcCnrk4aW3+lP8wJ1vptFhgXWoCM/Zf/bp3D8d90sY5BlyB3G0VWmvR83RDBJQbYNvDeThOoqsq1U/Oldn0A9xunFHfNNEXzRKT9bW6ZADwhjO87gpuqts/cfyFfKpj22amdCGTdPgY/+WFK5Pm2h1MmSmDOhEGW9v9RzrPCVtqbcym3JVU+0LAftdU2d79vi82FKkOWzZq3giDjS2+C9d+CBTU2JjVy1Kiri4EvOUJrN7zAdedl63sqnqdKQiDB4/wV/pviGvqd0XnGY3QPx8QYwwAgOX61x/yXVxiJM+Wv6Kp+/vKJ/3NnQ5hq30HW5qTqeh4HIgxphpb+ONqmhirLNe8iTFWAEAw0uXL34kVDc1ca3GBqm1LuaZiHmCctcY7bDCQQo2VSZzxp4THps77K+/HZPVW9pm0NBiaDZUzQwjpEUKxNjvXbTCAuHM7k/1w7325BJHBbjrq6+zqEUvs1VrzEyupr6aDcs0aW/gOz51QPkkAuj0S1FNKkIyLSR/GCGelR332Wpm6OgxijJV7D/Du8YkQ27BPv8o1HFqqVpI/Tc+05XN/0+/3pfUkgckbg8EYtRpJqcmKP4MhHFqeSh0oLaEPhcHRUTFn5/v+azSFRRUNwZG7ISY+jvSZMqlTpkzSEZ8A7sTFUe5HTxbGON5a4x1aFENM7XpHt2RGDXudomXfR1xb3CIylJxEjMYYiP6R30L5tzxBFmGE9DoCrViu6Hv3bWYVAARksGq3d9DAgmC36ZNmawrxbd/kqynJ9E1jLBoCAFFu1NXvAyW37wSe9nHSMpYleixmjf1hiaC1XWjQ6ZB7Q7H64znfw8eNy3y+/wW4yZAkH0RBLAAAAABJRU5ErkJggg=="},4083:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAYhJREFUOE+11DFII0EYBeD34kIsbAMJ2B4IWmhnqZW1IAeCVuJOyFgYsNDKBRsbC6NhZ5drLI67AwXRxtLOVtBC0MIuYiEBxTQ7+c2CStSsR0zc+vHxdt7sEl1+2GUP3w+GYTgoIkckF13X3Wv3Dd40DMNwol6v7zawahRF41rrqy+Dxpg8yW0APQAOROSuFUbyQUROa7XaXrFYrL7PvDY0xvwj+RPAI4DbT5plAfQCqJJccF33d3P2FfQ8z8lms39ITonIYj6f32yFxrlMJjPqOM4WgGGSM83omzNsRmP4s1HibC6XOxGRAWvtD631TVzgw7V5RlettTv/G8X3/aFUKnUGYEkptdESbHdVY0yF5LFSarorYBAElwDOlVKTHYPlcrnPcZz7xurrSqmVjsEgCOKlF6IoGtFan34ZfF54DcAygF9KqfmXs/+wcqlU6k+n03NJ44jIEMnRBtQP4G+lUpn1PC9KBH3fHyN5SLIvAb0GcGGtDQqFwn7ip9fudUnKf///sNOmTxE1ihUaro6hAAAAAElFTkSuQmCC"},5681:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAATdJREFUOE+1lLErRXEcxT8nBoM/wPBWpRjYjN5kVi+lmKQMbzAYvMlgsRjEoiwGobwSi9FmVQyKwUYGKTKp4/7q0n3ce3Xfu+87nz6/8zvn+/uJkkcl8+g+0PYwcA4sSWoWvUGLQ9uTwHEEewWqku7bBtpeBLaBHuAUeMmAvQNXQFNSOLhlfhzaPgKmgQ/gOcfZANAX36IuaT+pTQJ7gQOgFue3mQa1HXTjwBYwCswmob8zTEJreaXE4EtgCBiU9BQM/FmbWLgK7P1Xiu0R4BpYlrSRCizaqu1H4ELSTFnAuyj3G0lTHQNt9wNvUevrkhplAEPTdWBMUtjN9t5yXNwasALsSlr4zj6t5Qown1NOaDbsYdAdAnOSPvOAE8AZEPJJmwfgFtiRdJL59IquS5a++/9hp06/AIjjYBWrQVKNAAAAAElFTkSuQmCC"},2473:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAbNJREFUOE+tlLFqFVEURdcCCwPaW6QwHyAoKJpOUbuI2mmRQlBIoWCw0DLBSghEsVBRsLDQToNaqWiXQBQFP0ALCz9AMEXg5B2483zvZh5vhAzcYrh31jn37L1Hdvhxh3l0AkbEzSys3hnXQCswInYBZ4EVdTMiPhbgiXqvLrANGBH7gNfAYWBaXauAx4BV4DNwRv09CB0CRsQeYB2YBC6qb/LwILC8zwDPgV/AEfVPA62Bj3uHLgOn1ffNoRpYoKeAd73iT9Qr24ARkV39AF6os4PXaAMW6DPgAjClZrf/VI6IOeBBT4hD6reOwIM98b4Cc+qjGng/N4CJVLYjMN3wF3ioXquBT4Hj6lRthYhYLraZb9nLMX1SL9XA28AtYK+6Mc7AZYa7S4cL6mINbFSbUd92BKZ90rN9V/RtExFZ7TuQnkpvDc2x5ao5vxQkvzvQ3Kr24TngZVoHmB0FLfFrLHNefdVq7DKXG8ASsAZcVb9Uih8F7gIZwevqvZHRG0hGmjWVzVz/LCu395eV+Z1X8yZDz8jfV8l1gk8WcH6YoA8lTf38ju2wi8KjznT6wf5PgS0KwbkVHsAzMgAAAABJRU5ErkJggg=="},8737:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAc9JREFUOE+9lM1LG0EYxp93Z203ghCbbZuIB3PSQ1ss9FpYD6krIipFsqCHisfiuX+CSP+B3rSn7jY9pEjJigUX2oOgJ0XQk54aP1YR/Mhokx1ZY0BT4yY1OKed2Xd/88wzz76EOg+qMw//ALuN/V6CeFXNRgK0NGs++nG19hpQ6/ujKo0PskRIC+AoACpDwOAnZzFnpsUt1V4D6ka2jdCwIfA3bpuxzSCVPYYrymvvD8g5P1BCTf3lKi98+xJZ9ddrUogCa4UkTRFBLoN+ypjqpL+mJ915fno46KTjB3Xx8CaPK3ooCkwmiX0AiaJCj0z7a2S2BHnz1u1gDZjLcfbcSTcHK7wNqA1shJWHTYsAftuWOloxh9XERtPmZSX6bAaCwnz7sMtx4vxOQD3pThDwLud5LxXGevnW8mfH6cr/16X0JPeHBbzpPPKvf1rRhZpiU/6nJIZ2OplEv+DRuJ1Sp2vO4VWgNrQTDTFpEQLpjKWOl44XqPDyw6wAvgE4ImDND7GedOcAyHx7JRF6+uKjIBH25wSM5ApezEk92brRw2L6dw2Q1O4/F4ERSzf2xvjx6Xe/q3Qb7nsCqRcA4a3b1mOz4i0HdZdq3te9Y58DHHgLJE/jOQsAAAAASUVORK5CYII="},2418:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAPZJREFUOE/NlD9qAkEchb+HmjYKFqZK0tl7jfQ2HkKQFXKDyCbgMRKw8AheIGCplQpK0rmCVQj+wi4IO0pY3V1Dppx5fPPe/Hki56GceRwBzaOJqJ+0kTGRz1tc6wDNo4b4AAYY2wRoEdHCuJHP517rAtvcccWML+7VZ57k0rrYofZvgdamTIFrx2mRQD024dzZDs1jjSg7QCOQTyUdMDzTErcOcMdcLyzSAS/gMIzrRoZAfYJ0DuOX8s5KI77j8bNdivEkn8dswNDhPvKYZWaH//CndKhSiMphmEs5RE/B4wHRSIobre+Y6pnXX+vrJEiCKPfG/gEdVrgVt7yeFQAAAABJRU5ErkJggg=="},2799:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAT9JREFUOE+tlK1Pw1AUxc99XVtDgpkoDpKJeSzkpZ4ESUga2qkJguBPQCBRDL0+N8IEHtEUjyQBBW4I5EzbpJesSUk3wvqxPvtOfve8e/IOoeVDLfPwB2iEgxOA+9UG0Vssx/dF7TIw8CxTYMbEU4Dm66HcISYnSrED2//KtavAXVPgI0qxB9v/LHNphh6vausBA6end7Tt5FC9LIZtBDQCr08CAYNHsVTXjYB66N4SwwKnVyS0pxSYJNK/zFdR2+GvK+YuE42KsEYOMyeB0zOFdhxJdbMaUm2Hraesh+45gbpFMIO/E6nuGj05C4VoawnIPE+kumgIHAwJbGVAjafxgXotwmvv0Hh2H/Inp0x+IsdqI2ALoZx2DWHMwHisVA5UVg6L/xmcHbEQ+2XusvsU77HtT/6vr0qU9aLWG/sHWVPyFX0DsDgAAAAASUVORK5CYII="},3544:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAYAAADFw8lbAAAAAXNSR0IArs4c6QAABYFJREFUWEe9mXtsFFUUxr8zs6/uFglELSJgbXdKa3hpiBgJiOIz0WBjfFPYBQIKGiWNoiakFBMiJhZMUAMC3UIAkcSaEKMGJBUCCQZRgRTobkF5KIgRgd3tPmbmmNllF9ru4852y/yz2Zlzzv3dc+fee+43hD5cM0ZGx0iaWgvCGBCVgXkIEcqMkMw4D8I5GL+MQ7rGrRtOlh4qtDky6+ipiCgk63MBfhoEtyl/RoDBrdAsn/tOOPxmfIVBZw0PDWUHGkCYBcBippEMtnEwmimCxvWnXX+KxMoL+ixYdinhBgLqQXCKBBW2YYQZ+CjkdzZuA2m5/HKCzh7Gg/WS8FYQHhZuvBBDxk6py/n8ujP0bzb3rKAeJTgORK0ElBfStlkfBn4Hc63PX/prJt+MoF4lPAHgXUUf6nz0jLAKemij37m/p2kvUE95cAistJ8II/LF7Y/nzDhFrI9vDgy4cH38bqBPuP32MhraRoT7+gNCNCYz7T7PZx/9NqBEUz7dQD1VoU8ImC8a0Izd3VNljJok45cfNBzZk3OCJ8Iy8Kmvw7WgF+hMd6RSkrRjRVgje/E/MtOCF9+zpe9vWRbDjhY1Xz9VXZerWwKOTsMwnVGPEtxERC/l8zbzXJIBqx0YPUnGvCYbZMu1ARSBZYbP53d506B1SrTGAvUICJIZkFy2BuQrTTYMvIXQNCdaKKwa0/WxmwID2hNd9LiDK0iiN4sFacQpHQS8u9mB2yok+H/WssIancj1zrLOK32B0oUJUK8S6gShohigRiZfXmzDjpY4uq4Ab2+054Q17LYsi2dtmsEdvo7SkeQpv1xNNvlosSCN4R7/uAWXLjCW10XywubLaGIFiGk15HEHF5FEH/QVNPVOGpCpKx/soCEkMvvBOr1D3qrgVoCe6wtoJkgR2GhYtFX+kjxK8Ecimizq0tMuF2Q22OBFTkwuUVBm3k2equBxAlUVAioC2RM2+B8jHoUwZHKX4g7yKqFQIVWSGcgU7MGdKlYtiJnOCTNfNob+EhHdZMa7EMjUWio63NfzJEFNDv2NhkwPvZnJZHcC8z+2Y/RkWXgA+pLJVCPM1GZqeZr2mhXTXrfeUMhERpk2k8cdXkQSCy34S7c7UHYH4dRRHZXjcme1GJlMZ4TpLeEt9ObbCR/uKsHhPRpWzEkW3iNqJCz52tErw0WFTG2hRisiE2rKCxbMaLRhQ0MMbV8ki16rDVh9uPtRv+iQoGO+DmeNcJm3cK09UVPWP9CFi+c4nUUjo0ZmjavYkIn3U+cmX6C0Pglq6EkWrT3bMcTI3KoDJbDaCUufiSQqo7EPyjDOQdUTpMT94z9pWDlPfFsUnJEqq/Jdhk6VPht4ldBqEOZmCjB2iow3VtsTjzSVux0p/jqhY+9XKr5br0LPf2YT5LtqxljT7HfNM/6lQQ0RTHcgQISSntGMQnjq9GT5Fo8y2vfp+K0teZr85+y118AcRW5rZkSkCCpTIlr347I71EASlvQMUT5KwsRaSwLu+H4NcfPbtek+sI4lvoCrMeXYQylhyauEvgdR/4pi+bCZdzb7XY8BpGcBBWYPuzRYc1oPEPjOfPH64zkzdcpd8XvXnRnYTdnLKJJ5lNg4QnxvIeVfn+ATeilPzKToZZUdve7QPUxovVFiWVIcQ21zwHUwU2dzCrl1lVdulWXJ0Ejv71Om8jgzsE/T9NqNnQP+zmYqIo3bXEpoORG9CiC5mBbtoigzPgv5SxZtA+VcS/KCppjqKrpGWCR9MSR4iiCkqQy0xOLS+5tPlvwh0m9h0FSw5OcbdQ6Ap0BUI9JI2obZEDq2s2ZZ22+fbzIBTa+MuC2kPgmiagDDCWyo1MOv2p5m0CkApwnUrrP0TUpCNNW5q8b/A+7+gJC3OTplAAAAAElFTkSuQmCC"},4887:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAAXNJREFUSEvtlCFLBGEQhp8XDAZBo8IFD/wDNoPBeMFg02teMphNGuwWg4YDQYMgNoNg9A8YBJMg3IGGiwoGgzLuwCh67Mru7Z1XbuIyM8+878x+YkihIXEZgf/N+ZHVf1ptZtvAInAKnEt673U3ua02s1XgCDgB1gCH7gNNSc9FB8gFNrMKcJfAdiQdmtk4sA5sJurnYqADSfd5B8gEm9kGMB2NlgPgCrujBizEx0vg5kdCR1IzbZhUcCh8DFu9bhJ4yaHG816Bj8h1V2Ykdbprs8CzQEtSrlVkDWRmBlQltUuDzWzCD0vSmzeLfY9JcqW/ot/gM+BJ0laA95LjqiSHVR80eAVweDVALaAu6WKg4FB5DdwCbndN0nzanvtqdYCXgKsAN9LURl7/jutLmZm56qkstYME+y/n1/zwL79TjkfkO6XnHScddouAUnK9Pv8DEvs5Tp5Mt7NMtCU10hqUehLLTDQCl3GvUO3I6kJ2lUn+BKPesh8m5DemAAAAAElFTkSuQmCC"},9929:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAAZRJREFUSEvtlb1KA1EQRs9XCBaWFhYGLLQQtJJUChK0sDBIehs7W0vBNxAREd/AzkZBUFDQTgvFQsFCOwMKWghaCBZjLm4gbPbnJlkSwUyzLDN3zsw3s3tFh0wd4tIFt035vyW1mc0Ag0H775IOspYismMzewYGamAjkh6zhCdKbWb9wCtQlHTYNrADmdkHsCZpK3h3hbiCfK0sKRcOTl0uM7sBziWtBOC50BiSCtgMzpaaAe8BvZKKvi0GBS4A+0Be0lUz4HVgXtJog+AL4EtSIeqcj9TLwLakHl+wmU07iYGCJPesMx/wLHAC5CSVfeAV8BnQJykfF+8DHgYegBcnnQ8YGAJKktyMIy0VHCzKN3BcmXXdksTk/ZS0kVSkL/jebaikVc+OU8N8wUfAFPCWmvE3wI1kSdJlq1K7BZv0hFbDdiTFFurVcTWTmU0Ai8Bu5YdyXVtIkq+pzymU/Cm4Luv+v2YW68sCfAuMAXeSxkNFxfqyALtbyc37NDy/4AqN9LUMbnC5EsMbWq4uuBUF/p/UPxZNjh8DYeX1AAAAAElFTkSuQmCC"},7960:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAA99JREFUSEvtln9MllUUx8/3nPP+ABUERAxeyiKkTcsVsCTQonSTtdassWwssl+SK2rk5ljToDmtXI13aWuyVhSbW8wfw1zFKuGPUmn+CEt+ChZo4VJRGwS87/s87T4Igi3HZMs/5Pzx3Oc+O/d87v2ec+4e0HUyXCcuTYL/N+Unpb5BpG7a+cKcIOx4VSW31zqStKT8wpVHP3ug1Gd55HYlIreHW/qDodkMeFxw9UydV9xg/Jt2LouhoM4LwobxGzIlkpCt4JPJyz5vH/7qFFfjruefI5aPWJhEpDQ5Z8ubo8Fd+4rCvN6o/cw8X5hPDwYGs1wq+4loBkDbpt+1Lu/g1pWusOgLXxHRQwARALo8gphhg9nfOaVrTXZ2XdABt3+zMnLwb3SCOUJUDicv3ZI6GvznwdLXwPIuM1sitGIwENgLog4QeQCsjb67ZGPT9uWbCLR6CEkEdujEZgPMzpyZbSZadUvOJ1tH2qn5iwI/QV4VFUtIM25bWvajgZ/6fm2KuvkQi04RkZqo1p8e7vbNSRWlA05kwRO9R7t290cM5tsEFRnaMrN5ioERC1KY+SUCCwOViUvK80fAjdUFi0SljkXAKhVJi/3P1NY+oCnerGpRzRHhnlDISo9LL+34/Yc1TxHhMyOfBUqPX/D2oau1g20TTu0t+MMmxAGo9D344WVwVVWuzA+PbWCVuSxyuieqLzFhIOZRC6gSVZP71bGpJWUG0FlXtIHBr4PRbwcHkgcvngnY6ook8jh8z9BgypDAlkn2cgJKYIxRfNPCze+Mubla96wqhOr7omKz6DpRfZlFZolI/UnpXpiWVh4w4X799pXtAD0O4Jw74Lq5N3C+ghiPOcVkpDW5vZRXZ85kEm2wXVag75747PIzY8BtXxb6SHCCWVRcElBRZZU+iznTd+96p2VsInTUvNgAwp0AN3+674O5+RkF9SCkmQIypeXk9dL7EBg2wK1KVm70fWU/mzj/uquPf124DaJPigqpS20Ib0rI2Fg8LN6xqtyp7vDpxwkUB+ZdnshzeVbvzBZiJIrgCFnY4xSWc2Iil2ivTVbT2dD52juyPv5rTB+PLoy2msJHmHW3AbNoYyjUnXprdkX/sE/bjjxfSNytYITZhA0R08Qfgvc3EIcT0xu+Rf71Vyu0/wSfqF3htUNRx0R1lrh0sS/zLXNRjNgvO/LSGVJvskbA0xGxM4/CCh02CSRQfnzme5XXBDaLWr4rSvCoHTH7fn/TlUGaq5+dFrL6k5wuZW97TMwMGoDtzMOkvyNmweaL1wwez8KJ+kz+CExUwXGvn5R63FJN1PHGk/ofOv0aLu195j8AAAAASUVORK5CYII="},4526:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAfxJREFUOE/FlD+IE1EQxr/ZXcIFThRioZUBUy0SsrMn2liINpITW0WvsxBBtD9ERFREBMVCe7UQC0/Mdf7DSsGd3SwSECQHdlarIBbJvh15EuE8c5uIB06zPPbN733zzbxH2OCgDebh/wDDMNwzHA6RpunbSRWNVej7/mylUjnguu48gHlV/WJBRLQFQMcY0xkMBs97vd63tQf8AWTm4wDuAnhDRB1VXRaRjzaRmRtE1FZVe9BeACdF5OFq6DjgBbtBRC6WlcfMY/dNBLZarbbjOOcsvCiKm0mSLI/UTg1cJKKZKIrOB0EwR0T3VPXMyMPbqroQx/G7IAiuOo7zPYqiS6UlB0FwlIiOiMgxZl4E4P0qf1RmLiKXmfm1qj6J4/jGJKBVdUdEdjPzCQBtC7dgZn4K4IGI3GfmzwBOicjjUmC9Xp+p1WorxphDSZK8Z+ZHRNRQ1RkAO/I830dEmz3PW8qybHu/3/9aCrQ/wzA8q6pzIrJg181mc5f9Oo7TcF3X+vgJwCsRsZb8FmMHe6QytvMYRdGt1RnM/IKImlmW7Vyr7mfj1ps13/e3VavVlwCWjDHXiqKY9TzvCoCDeZ7vT9P0w7jc0schDMOtqnodwGHrm6o+y/P8dJqmK+sJmeq1sVfOGLOp2+1aG0pjKuAkyMQu/w1gqi7/C/AHmv/YFdypXC0AAAAASUVORK5CYII="},6428:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAWVJREFUOE+91L9Lw0AUB/B3uQz9ExwEHQShIH29qQ4O7gUdBHFyEiwo0k3RUUEQ6aCi4OQkgoNC/wMHXfruZSm6KTj4J2To3UkggaBJtD9oxnD3yffdezkBI37EiD0YP1itVqeEEPWoEudcW2v9UVRVYUJEXJdSHjvn7iNECLFijNll5ps8NBdExGkp5bMxZp6Z3yMg691POBdUSm0BwCwRbac3KaXOAOCViC6yUg4KvhHReV9gUp61tpY0YqiS4zPb8DzvCADu4jSr1tp9Zr4epClLUsor59wnALzEQE0IMWmM2WTmx3+XHI/LYa/XawRB0E5vrFQqdd/3L40xB1nj86spSqkZANDW2gVm5qwUiIie5z1Za+eSkUrWZYEnAOATUbPoj1BKtZxzodZ6L70uC4y+fMrMD0UgIi5LKXc6nc7iX+BtGIbNbrf7VQSWy+WJUqnUIqK1QnDY62z811e/iUee8Bu8xqAV5aJ5yQAAAABJRU5ErkJggg=="},5365:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAA4ZJREFUWEftWE+IT2EUPacoysKCWFiYsqBslDITk5lsKMViJskoQsyKBTVTZGQxsyCUMktCNhaEKCMjIyOWFsrCFAtiYTFlirre0X315jfve3+Hfmm++m1+73v3nXfuPefe7xFNvtjk+DAHsG6G/l8GzWwBgM0AdgJYC2CF/34B+AzgE4C3AO4AeEZyqgqbpRk0s8UAjgI4DmBRwYdORnvPAbhE8nvBe/5sKwXQzPYAuAhgiT9kHMAjAE+cMbGmJTZXOsNboz2t/v83AMdI3iwKshBAM5sH4CyAPg88CqCfpADmLjMTwEEAHb55CMApkiqHzJUL0MHdiuqpC4ACHiJ5NS9w2nUzOwBgGIBe+DaA3XkgiwDUm4s5FX43ybEq4OJ7zEws6oWXAxgi2Z8VLxOg19wNZ66zLrgGkI+dyZ6smgwCdLW+d0Hsr5rWEDtmdtjTrcysCak7C+DpKPhAVNijJDvrpDUD5FMXzgDJM2n7UgG6CX91n2tLU6uZyQO3AHiQVehmJkvaSPJuIwBX98vIU+WTS9PMPARQ3vUQwDjJtoAiZTsns9RoZhKCWFoddRPVsOxp2jKz1wDWA9hGUp46bYUAXolUe0QpDlFvZgqqh4vJYZK9ycgN4N4BWJfGkJnFpTQjhuKFAIp2mWt7lnLNbBOAewBGSHYnVJpkTuDEnsQwY7nt6EXHSLYXZfCDt6oWkhNZAnG1T8Z1mMJcEJzimplaop43QbKlKMCf7lHz85w+J62Z4BygpqIfUTOYIrmwKEDdoBsLAyzLXKIcKgEsnGJnoXDNpai4UoqfR4UvAaRaQ920Ntyv3lxaJLk2U4Q5M5PCpd7eUC1XtZkiRp2ZVu80H6MBVxN4cLSqatQq3LjVtZJ8lVI78RgW9Dn3SXUkmfmMTmFmG9StSrc6T1/msGBmqwBoIjkfMmGPo46zyyfoaQcnM6s2LHhgpSYet/ZFJnoty7DLXqs9bjnIHgDXfWDtIPmiLJBAe9NxdcSbwV6SGopTV9mRv6suyCitAifRaAyrN/I7izrgJA9NB6um29N6eVYPTQmQjcfOvjR1B1IqteqoOfvHzgbXV01eSBzc30TD5n0dCzSNRIb8xfcv82lIgLb7QKpLMu0TWTVXaFgoMF4156ePBjZl5mJoR+LjkbpLzFT88UhnER28/s3Ho9mwmTIxcm2mTLC/sXcOYF1Wm57B35ZosTioaURLAAAAAElFTkSuQmCC"},8056:function(t,e,i){t.exports=i.p+"img/meal-icon-person.3f29332a.png"},901:function(t){t.exports="data:image/png;base64,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"},5361:function(t){t.exports="data:image/png;base64,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"},8681:function(t){t.exports="data:image/png;base64,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"},3772:function(t,e,i){t.exports=i.p+"img/vip-icon-year.0384b0a0.png"},8594:function(t,e,i){t.exports=i.p+"img/big-vip-hedaer-bg.14db184c.png"},7302:function(t,e,i){t.exports=i.p+"img/big-vip-person.477085db.png"},1995:function(t,e,i){t.exports=i.p+"img/create-header.f4f05b86.png"},6375:function(t,e,i){t.exports=i.p+"img/draw-loading-img.35d5305f.png"}},e={};function i(s){var a=e[s];if(void 0!==a)return a.exports;var o=e[s]={id:s,loaded:!1,exports:{}};return t[s].call(o.exports,o,o.exports,i),o.loaded=!0,o.exports}i.m=t,function(){i.amdO={}}(),function(){var t=[];i.O=function(e,s,a,o){if(!s){var n=1/0;for(d=0;d<t.length;d++){s=t[d][0],a=t[d][1],o=t[d][2];for(var l=!0,c=0;c<s.length;c++)(!1&o||n>=o)&&Object.keys(i.O).every((function(t){return i.O[t](s[c])}))?s.splice(c--,1):(l=!1,o<n&&(n=o));if(l){t.splice(d--,1);var r=a();void 0!==r&&(e=r)}}return e}o=o||0;for(var d=t.length;d>0&&t[d-1][2]>o;d--)t[d]=t[d-1];t[d]=[s,a,o]}}(),function(){i.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return i.d(e,{a:e}),e}}(),function(){i.d=function(t,e){for(var s in e)i.o(e,s)&&!i.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:e[s]})}}(),function(){i.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"===typeof window)return window}}()}(),function(){i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}}(),function(){i.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}}(),function(){i.nmd=function(t){return t.paths=[],t.children||(t.children=[]),t}}(),function(){i.p="/"}(),function(){var t={524:0};i.O.j=function(e){return 0===t[e]};var e=function(e,s){var a,o,n=s[0],l=s[1],c=s[2],r=0;if(n.some((function(e){return 0!==t[e]}))){for(a in l)i.o(l,a)&&(i.m[a]=l[a]);if(c)var d=c(i)}for(e&&e(s);r<n.length;r++)o=n[r],i.o(t,o)&&t[o]&&t[o][0](),t[o]=0;return i.O(d)},s=self["webpackChunkxiaoyi_pc_ui"]=self["webpackChunkxiaoyi_pc_ui"]||[];s.forEach(e.bind(null,0)),s.push=e.bind(null,s.push.bind(s))}();var s=i.O(void 0,[504],(function(){return i(8901)}));s=i.O(s)})();
//# sourceMappingURL=app.0c06ce6c.js.map