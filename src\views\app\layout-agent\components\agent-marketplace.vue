<template>
  <div class="agent-marketplace">
    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索智能体名称或描述"
          prefix-icon="el-icon-search"
          @input="handleSearch"
          clearable
        />
      </div>
      <div class="filter-bar">
        <div class="filter-item">
          <span class="label">分类:</span>
          <el-select v-model="selectedCategory" placeholder="全部分类" @change="handleCategoryChange">
            <el-option label="全部" value=""></el-option>
            <el-option label="小艾智能体" value="xiaoai"></el-option>
            <el-option label="Python编程助手" value="python"></el-option>
            <el-option label="英语学习助手" value="english"></el-option>
            <el-option label="深度学习专家" value="deeplearning"></el-option>
            <el-option label="UI设计师" value="ui"></el-option>
            <el-option label="英语口语老师" value="speaking"></el-option>
          </el-select>
        </div>
        <div class="filter-item">
          <span class="label">排序:</span>
          <el-select v-model="sortBy" placeholder="默认排序" @change="handleSort">
            <el-option label="默认排序" value="default"></el-option>
            <el-option label="最新发布" value="newest"></el-option>
            <el-option label="最受欢迎" value="popular"></el-option>
            <el-option label="评分最高" value="rating"></el-option>
          </el-select>
        </div>
      </div>
    </div>

    <!-- 智能体列表 -->
    <div class="agent-list" v-loading="loading">
      <div class="agent-grid">
        <div 
          v-for="agent in filteredAgents" 
          :key="agent.id"
          class="agent-card"
          @click="selectAgent(agent)"
        >
          <div class="agent-avatar">
            <img :src="agent.avatar || defaultAvatar" :alt="agent.name" />
            <div class="agent-status" :class="agent.status">
              {{ agent.status === 'online' ? '在线' : '离线' }}
            </div>
          </div>
          <div class="agent-info">
            <h3 class="agent-name">{{ agent.name }}</h3>
            <p class="agent-description">{{ agent.description }}</p>
            <div class="agent-stats">
              <span class="rating">
                <i class="el-icon-star-on"></i>
                {{ agent.rating }}
              </span>
              <span class="usage-count">{{ agent.usageCount }}次使用</span>
            </div>
            <div class="agent-tags">
              <el-tag 
                v-for="tag in agent.tags" 
                :key="tag" 
                size="mini"
                type="info"
              >
                {{ tag }}
              </el-tag>
            </div>
            <div class="agent-price">
              <span v-if="agent.price === 0" class="free">免费</span>
              <span v-else class="paid">¥{{ agent.price }}/次</span>
            </div>
          </div>
          <div class="agent-actions">
            <el-button type="primary" size="small" @click.stop="selectAgent(agent)">
              立即使用
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="filteredAgents.length === 0 && !loading" class="empty-state">
        <i class="el-icon-search"></i>
        <p>没有找到相关智能体</p>
      </div>
    </div>
  </div>
</template>

<script>
import { getAgentListApi } from '@/apis/tools'

export default {
  name: 'agent-marketplace',
  
  data() {
    return {
      loading: false,
      searchKeyword: '',
      selectedCategory: '',
      sortBy: 'default',
      agents: [],
      defaultAvatar: 'https://ai-v2.deepcity.cn/default/head.png'
    }
  },
  
  computed: {
    filteredAgents() {
      let result = [...this.agents]
      
      // 搜索过滤
      if (this.searchKeyword) {
        result = result.filter(agent => 
          agent.name.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
          agent.description.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
      }
      
      // 分类过滤
      if (this.selectedCategory) {
        result = result.filter(agent => agent.category === this.selectedCategory)
      }
      
      // 排序
      switch (this.sortBy) {
        case 'newest':
          result.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
          break
        case 'popular':
          result.sort((a, b) => b.usageCount - a.usageCount)
          break
        case 'rating':
          result.sort((a, b) => b.rating - a.rating)
          break
      }
      
      return result
    }
  },
  
  async mounted() {
    await this.loadAgents()
  },
  
  methods: {
    async loadAgents() {
      this.loading = true
      try {
        const res = await getAgentListApi({ agentVendorSign: '', agentType: 'text' })
        // 模拟数据，实际应该从API获取
        this.agents = this.mockAgentData(res.data || [])
      } catch (error) {
        console.error('加载智能体列表失败:', error)
        this.$message.error('加载智能体列表失败')
      } finally {
        this.loading = false
      }
    },
    
    mockAgentData(apiData) {
      // 基于API数据创建模拟数据
      const mockAgents = [
        {
          id: '1',
          name: '小艾智能体',
          description: '专业的AI助手，能够回答各种问题，提供智能建议和解决方案',
          avatar: '',
          status: 'online',
          rating: 4.8,
          usageCount: 1580,
          tags: ['智能助手', '问答'],
          price: 0,
          category: 'xiaoai',
          createTime: '2024-01-15'
        },
        {
          id: '2', 
          name: 'Python编程助手',
          description: '专业的Python编程指导，提供代码优化建议，解决编程难题',
          avatar: '',
          status: 'online',
          rating: 4.9,
          usageCount: 2340,
          tags: ['编程', 'Python'],
          price: 0,
          category: 'python',
          createTime: '2024-01-10'
        },
        {
          id: '3',
          name: '英语学习助手', 
          description: '帮助提升英语水平，提供语法纠错、词汇学习和口语练习',
          avatar: '',
          status: 'online',
          rating: 4.7,
          usageCount: 890,
          tags: ['英语', '学习'],
          price: 0,
          category: 'english',
          createTime: '2024-01-08'
        },
        {
          id: '4',
          name: '深度学习专家',
          description: '专业的深度学习和机器学习指导，提供算法优化和模型训练建议',
          avatar: '',
          status: 'online', 
          rating: 4.9,
          usageCount: 1560,
          tags: ['深度学习', 'AI'],
          price: 0,
          category: 'deeplearning',
          createTime: '2024-01-05'
        },
        {
          id: '5',
          name: 'UI设计师',
          description: '专业的UI/UX设计指导，提供设计建议和用户体验优化方案',
          avatar: '',
          status: 'online',
          rating: 4.6,
          usageCount: 720,
          tags: ['设计', 'UI/UX'],
          price: 0,
          category: 'ui',
          createTime: '2024-01-03'
        },
        {
          id: '6',
          name: '英语口语老师',
          description: '专业的英语口语训练，提供发音纠正和对话练习',
          avatar: '',
          status: 'online',
          rating: 4.8,
          usageCount: 1200,
          tags: ['英语', '口语'],
          price: 0,
          category: 'speaking',
          createTime: '2024-01-01'
        }
      ]
      
      return mockAgents
    },
    
    handleSearch() {
      // 搜索逻辑已在computed中处理
    },
    
    handleCategoryChange() {
      // 分类变化逻辑已在computed中处理
    },
    
    handleSort() {
      // 排序逻辑已在computed中处理
    },
    
    selectAgent(agent) {
      this.$emit('select-agent', agent)
    }
  }
}
</script>

<style lang="scss" scoped>
.agent-marketplace {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  
  .search-section {
    background: white;
    padding: 20px;
    border-bottom: 1px solid #e8e8e8;
    
    .search-bar {
      margin-bottom: 16px;
      
      .el-input {
        max-width: 400px;
      }
    }
    
    .filter-bar {
      display: flex;
      gap: 20px;
      
      .filter-item {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .label {
          font-size: 14px;
          color: #666;
          white-space: nowrap;
        }
        
        .el-select {
          width: 150px;
        }
      }
    }
  }
  
  .agent-list {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    
    .agent-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: 20px;
    }
    
    .agent-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }
      
      .agent-avatar {
        position: relative;
        text-align: center;
        margin-bottom: 16px;
        
        img {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          object-fit: cover;
        }
        
        .agent-status {
          position: absolute;
          top: 0;
          right: calc(50% - 40px);
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          color: white;
          
          &.online {
            background: #52c41a;
          }
          
          &.offline {
            background: #999;
          }
        }
      }
      
      .agent-info {
        .agent-name {
          font-size: 18px;
          font-weight: 600;
          margin: 0 0 8px 0;
          color: #333;
        }
        
        .agent-description {
          font-size: 14px;
          color: #666;
          line-height: 1.5;
          margin: 0 0 12px 0;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .agent-stats {
          display: flex;
          justify-content: space-between;
          margin-bottom: 12px;
          font-size: 14px;
          
          .rating {
            color: #faad14;
            
            i {
              margin-right: 4px;
            }
          }
          
          .usage-count {
            color: #999;
          }
        }
        
        .agent-tags {
          margin-bottom: 12px;
          
          .el-tag {
            margin-right: 8px;
            margin-bottom: 4px;
          }
        }
        
        .agent-price {
          .free {
            color: #52c41a;
            font-weight: 600;
          }
          
          .paid {
            color: #7848f1;
            font-weight: 600;
          }
        }
      }
      
      .agent-actions {
        margin-top: 16px;
        text-align: center;
        
        .el-button {
          width: 100%;
        }
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #999;
      
      i {
        font-size: 48px;
        margin-bottom: 16px;
        display: block;
      }
      
      p {
        font-size: 16px;
        margin: 0;
      }
    }
  }
}
</style>
