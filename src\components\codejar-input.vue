<template>
    <div class="codejar-input">
        <code-jar :value="_content" @input="onInput" :highlighter="highlight"/>
    </div>
</template>

<script>

import CodeJar from "vue-codejar";
import hljs from 'highlight.js';
import 'highlight.js/styles/base16/tomorrow.css';

hljs.configure({
    languages: ['C++', 'HTML', 'XML', 'Bash', 'C#', 'JSON', 'CSS', 'Markdown',
        'Ruby', 'Go', 'GraphQL', 'INI', 'Java', 'Less', 'Lua', 'Makefile', 'Perl'
        , 'R', 'Rust', 'SCSS', 'Shell-Session', 'SQL', 'Swift', 'TypeScript',
        'AccessLog', 'Lisp', 'Clojure-REPL', 'Clojure', 'Scala'
        , 'Objective-C', 'PHP', 'PHP-Template', 'Python', 'Python-REPL',
        'Erlang', 'YAML', 'INI', 'NginxConf', 'ApacheConf'
        , 'WebAssembly', 'VB'
        , 'JavaScript', 'Kotlin']
})

export default {

    name: "codejar-input",
    components: {hljs, CodeJar},

    data() {
        return {
            // 这里判断
            highlight: (editor) => {
                const code = editor.textContent
                let h = hljs.highlightAuto(code)
                if (h.secondBest?.language == 'JavaScript') {
                    editor.innerHTML = h.secondBest.value
                } else if (h.language && h.relevance > 2) {
                    editor.innerHTML = h.value
                }
            },
            item: {}
        }
    },

    props: {
        _content: String,
    },

    mounted() {},

    watch: {},

    computed: {},

    methods: {

        // onInput: _.debounce(function (val) {
        //     let item = _.cloneDeep(this._item)
        //     item.gist = val
        //     this.$emit('input', item)
        // }, 500)
        // onInput(val) {
        //     this.$emit('input', {id: this._itemId, gist: this._itemGist})
        // },

        onInput: function (val) {
            this.$emit('input', val)
        }
    }
}
</script>

<style lang="scss">

.codejar-input {

    & > div {
        height: 100%;
    }

    .jar-editor {
        height: 100% !important;
        //font-family: PincoMono, Consolas, Menlo, "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;;
        //font-family: Consolas, Monaco, monospace;
        //font-family: "Montserrat", "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
        //background: rgba(93, 100, 148, 0.1) !important;
        //background: #23241f !important;
        box-shadow: none;
        font-size: 13px;
        word-break: break-all;
    }
}
</style>
