<template>
	<el-aside class="layout-left">
		<div class="header">
			<div class="title">ChatGPT Next Vue</div>
			<div class="sub-title">Build your own AI assistant.</div>
			<div class="flex"></div>
		</div>
		<div class="header-bar">
			<button class="mr10">
				<svg
					class="mr4"
					xmlns="http://www.w3.org/2000/svg"
					xmlns:xlink="http://www.w3.org/1999/xlink"
					width="16"
					height="16"
					fill="none"
				>
					<defs>
						<path id="mask_svg__a" d="M0 0h16v16H0z"></path>
					</defs>
					<g>
						<mask id="mask_svg__b" fill="#fff">
							<use xlink:href="#mask_svg__a"></use>
						</mask>
						<g mask="url(#mask_svg__b)">
							<path
								d="M6 0C2.69 0 0 2.54 0 5.67s2.69 5.66 6 5.66 6-2.53 6-5.66C12 2.54 9.31 0 6 0Z"
								transform="translate(2 3.333)"
								style="
									stroke: rgb(51, 51, 51);
									stroke-width: 1.33333;
									stroke-opacity: 1;
									stroke-dasharray: 0, 0;
								"
							></path>
							<path
								d="M1 0C.45 0 0 .6 0 1.33c0 .74.45 1.34 1 1.34s1-.6 1-1.34C2 .6 1.55 0 1 0Z"
								transform="rotate(15 -22.183 22.313)"
								style="
									stroke: rgb(51, 51, 51);
									stroke-width: 1.33333;
									stroke-opacity: 1;
									stroke-dasharray: 0, 0;
								"
							></path>
							<path
								d="M1 0C.45 0 0 .6 0 1.33c0 .74.45 1.34 1 1.34s1-.6 1-1.34C2 .6 1.55 0 1 0Z"
								transform="rotate(165 5.213 5.304)"
								style="
									stroke: rgb(51, 51, 51);
									stroke-width: 1.33333;
									stroke-opacity: 1;
									stroke-dasharray: 0, 0;
								"
							></path>
							<path
								d="M4 4.51c1.04-1.04 1.15-2.74.1-3.78C3.06-.32 1.04-.2 0 .84"
								transform="translate(9.667 2.493)"
								style="
									stroke: rgb(51, 51, 51);
									stroke-width: 1.33333;
									stroke-opacity: 1;
									stroke-dasharray: 0, 0;
								"
							></path>
							<path
								d="M.84 4.51C-.2 3.47-.32 1.77.73.73 1.77-.32 3.8-.2 4.84.84"
								transform="translate(1.493 2.493)"
								style="
									stroke: rgb(51, 51, 51);
									stroke-width: 1.33333;
									stroke-opacity: 1;
									stroke-dasharray: 0, 0;
								"
							></path>
							<path
								d="M0 0c.17.43.73 1.09 1.67.29.93.8 1.5.14 1.66-.29"
								transform="translate(6.5 11.67)"
								style="
									stroke: rgb(51, 51, 51);
									stroke-width: 1.33333;
									stroke-opacity: 1;
									stroke-dasharray: 0, 0;
								"
							></path>
						</g>
					</g>
				</svg>
				面具
			</button>
			<button>
				<svg
					class="mr4"
					xmlns="http://www.w3.org/2000/svg"
					xmlns:xlink="http://www.w3.org/1999/xlink"
					width="16"
					height="16"
					fill="none"
				>
					<g>
						<mask id="plugin_svg__b" fill="#fff">
							<use xlink:href="#plugin_svg__a"></use>
						</mask>
						<g mask="url(#plugin_svg__b)">
							<path
								d="M6.945 1.725c.261.26.392.576.392.945V6c0 .37-.13.684-.392.945A1.288 1.288 0 0 1 6 7.337H2.67c-.37 0-.684-.13-.945-.392A1.288 1.288 0 0 1 1.333 6V2.67c0-.37.13-.684.392-.945.26-.261.576-.392.945-.392H6c.37 0 .684.13.945.392zM2.667 6c0 .002 0 .003.003.003H6c.002 0 .003 0 .003-.003V2.67c0-.002 0-.003-.003-.003H2.67c-.002 0-.003 0-.003.003zM6.945 9.058c.261.261.392.576.392.945v3.33c0 .37-.13.685-.392.946A1.288 1.288 0 0 1 6 14.67H2.67c-.37 0-.684-.13-.945-.391a1.288 1.288 0 0 1-.392-.946v-3.33c0-.369.13-.684.392-.945.26-.26.576-.391.945-.391H6c.37 0 .684.13.945.391zm-4.278 4.275c0 .003 0 .004.003.004H6c.002 0 .003-.001.003-.004v-3.33c0-.002 0-.003-.003-.003H2.67c-.002 0-.003.001-.003.003zM13.789 6.455a2.987 2.987 0 0 1-2.126.882c-.407 0-.797-.08-1.169-.238a2.97 2.97 0 0 1-.952-.645 2.983 2.983 0 0 1-.64-.956 2.984 2.984 0 0 1-.235-1.168 2.974 2.974 0 0 1 .876-2.12 2.973 2.973 0 0 1 2.12-.877c.407 0 .796.079 1.169.235.36.151.678.365.955.64a2.97 2.97 0 0 1 .883 2.122 2.988 2.988 0 0 1-.882 2.125zm-2.126-3.788c-.46 0-.853.162-1.177.486A1.603 1.603 0 0 0 10 4.33c0 .462.162.857.487 1.184.325.326.717.49 1.176.49.461 0 .855-.164 1.183-.492.327-.327.49-.721.49-1.182 0-.46-.163-.851-.489-1.176a1.618 1.618 0 0 0-1.184-.487zM14.278 9.058c.261.261.392.576.392.945v3.33c0 .37-.13.685-.392.946a1.288 1.288 0 0 1-.945.391h-3.33c-.369 0-.684-.13-.945-.391a1.288 1.288 0 0 1-.391-.946v-3.33c0-.369.13-.684.391-.945.261-.26.576-.391.945-.391h3.33c.37 0 .684.13.945.391zM10 13.333c0 .003.001.004.003.004h3.33c.003 0 .004-.001.004-.004v-3.33c0-.002-.001-.003-.004-.003h-3.33c-.002 0-.003.001-.003.003z"
								style="fill: rgb(51, 51, 51); opacity: 1"
							></path>
						</g>
					</g>
					<defs>
						<path id="plugin_svg__a" d="M0 0h16v16H0z"></path>
					</defs>
				</svg>
				插件
			</button>
		</div>
		<div class="chat-vlist">
			<div
				class="item"
				@click="chooseChat(item)"
				:class="chatIdSelected == item.id ? 'active' : 'XXX'"
				v-for="(item, idx) of chatItems"
				:key="idx"
			>
				<div class="item-title">{{ item.title }}</div>
				<div class="info">
					<div class="count">{{ item.messages.length }} 条对话</div>
					<div class="date">{{ item.created }}</div>
				</div>
				<div class="btn-delete" @click="deleteChat(item)">
					<feather-icon icon="XCircleIcon"></feather-icon>
				</div>
			</div>
		</div>
		<div class="tail">
			<div class="inline-flex">
				<button class="mr10">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						xmlns:xlink="http://www.w3.org/1999/xlink"
						width="16"
						height="16"
						fill="none"
					>
						<defs>
							<path id="settings_svg__a" d="M0 0h16v16H0z"></path>
						</defs>
						<g>
							<mask id="settings_svg__b" fill="#fff">
								<use xlink:href="#settings_svg__a"></use>
							</mask>
							<g mask="url(#settings_svg__b)">
								<path
									transform="translate(1.333 2.333)"
									d="M13.33 5.67 10 0H3.33L0 5.67l3.33 5.66H10l3.33-5.66Z"
									style="
										stroke: rgb(51, 51, 51);
										stroke-width: 1.33333;
										stroke-opacity: 1;
										stroke-dasharray: 0, 0;
									"
								></path>
								<path
									transform="translate(6.333 6.333)"
									d="M3.33 1.67C3.33.75 2.59 0 1.67 0 .75 0 0 .75 0 1.67c0 .92.75 1.66 1.67 1.66.92 0 1.66-.74 1.66-1.66Z"
									style="
										stroke: rgb(51, 51, 51);
										stroke-width: 1.33333;
										stroke-opacity: 1;
										stroke-dasharray: 0, 0;
									"
								></path>
							</g>
						</g>
					</svg>
				</button>
				<button>
					<svg
						xmlns="http://www.w3.org/2000/svg"
						xmlns:xlink="http://www.w3.org/1999/xlink"
						width="16"
						height="16"
						fill="none"
					>
						<defs>
							<path id="github_svg__a" d="M0 0h16v16H0z"></path>
						</defs>
						<g>
							<mask id="github_svg__b" fill="#fff">
								<use xlink:href="#github_svg__a"></use>
							</mask>
							<g mask="url(#github_svg__b)">
								<path
									d="M7.11 8.51c.81-.16 1.53-.45 2.1-.87.96-.73 1.46-1.85 1.46-2.95 0-.78-.3-1.5-.81-2.11-.28-.34.55-2.89-.19-2.55-.73.34-1.81 1.1-2.38.94C6.68.79 6.02.69 5.33.69c-.6 0-1.17.07-1.71.21-.79.2-1.53-.54-2.29-.87C.58-.29.99 2.34.77 2.62.28 3.22 0 3.93 0 4.69c0 1.1.6 2.22 1.56 2.95.65.48 1.45.78 2.35.94"
									transform="translate(2.667 1.645)"
									style="
										stroke: rgb(51, 51, 51);
										stroke-width: 1.33333;
										stroke-opacity: 1;
										stroke-dasharray: 0, 0;
									"
								></path>
								<path
									d="M.58 0C.19.43 0 .83 0 1.21v2.91"
									transform="translate(6 10.22)"
									style="
										stroke: rgb(51, 51, 51);
										stroke-width: 1.33333;
										stroke-opacity: 1;
										stroke-dasharray: 0, 0;
									"
								></path>
								<path
									d="M0 0c.37.48.55.91.55 1.29v2.89"
									transform="translate(9.782 10.159)"
									style="
										stroke: rgb(51, 51, 51);
										stroke-width: 1.33333;
										stroke-opacity: 1;
										stroke-dasharray: 0, 0;
									"
								></path>
								<path
									d="M0 0c.3.04.52.17.67.41C.88.77 1.69 2.1 2.61 2.1H4"
									transform="translate(2 10.405)"
									style="
										stroke: rgb(51, 51, 51);
										stroke-width: 1.33333;
										stroke-opacity: 1;
										stroke-dasharray: 0, 0;
									"
								></path>
							</g>
						</g>
					</svg>
				</button>
			</div>
			<button @click="addChat">
				<svg
					class="mr4"
					xmlns="http://www.w3.org/2000/svg"
					xmlns:xlink="http://www.w3.org/1999/xlink"
					width="16"
					height="16"
					fill="none"
				>
					<defs>
						<path id="add_svg__a" d="M0 0h16v16H0z"></path>
					</defs>
					<g>
						<mask id="add_svg__b" fill="#fff">
							<use xlink:href="#add_svg__a"></use>
						</mask>
						<g mask="url(#add_svg__b)">
							<path
								d="M13.33 6.67A6.66 6.66 0 0 0 6.67 0C2.98 0 0 2.98 0 6.67a6.66 6.66 0 0 0 6.67 6.66c3.68 0 6.66-2.98 6.66-6.66Z"
								transform="translate(1.333 1.333)"
								style="
									stroke: rgb(51, 51, 51);
									stroke-width: 1.33333;
									stroke-opacity: 1;
									stroke-dasharray: 0, 0;
								"
							></path>
							<path
								d="M0 0v5.33"
								transform="translate(8 5.333)"
								style="
									stroke: rgb(51, 51, 51);
									stroke-width: 1.33333;
									stroke-opacity: 1;
									stroke-dasharray: 0, 0;
								"
							></path>
							<path
								d="M0 0h5.33"
								transform="translate(5.333 8)"
								style="
									stroke: rgb(51, 51, 51);
									stroke-width: 1.33333;
									stroke-opacity: 1;
									stroke-dasharray: 0, 0;
								"
							></path>
						</g>
					</g>
				</svg>
				新的聊天
			</button>
		</div>
	</el-aside>
</template>

<script>
import { ChatItem } from '@/defines/ChatItem';
import { getMerchantPcToolsUrls } from '@/apis/chat';

export default {
	name: 'layout-left',

	data() {
		return { urls: [] };
	},

	async mounted() {
		this.urls = await getMerchantPcToolsUrls();
		console.log(this.urls);
	},

	methods: {
		addChat() {
			let item = ChatItem({ title: '新的聊天' });
			this.chatItems.push(item);
			this.$store.state.chatIdSelected = item.id;
		},

		chooseChat({ id }) {
			this.$store.state.chatIdSelected = id;
		},

		deleteChat({ id }) {
			this.$store.state.chatItems = this.$store.state.chatItems.filter(t => t.id != id);
			if (this.$store.state.chatIdSelected == id) {
				this.$store.state.chatIdSelected = null;
			}
		},
	},

	computed: {
		chatItems() {
			return this.$store.state.chatItems;
		},

		chatIdSelected() {
			return this.$store.state.chatIdSelected;
		},

		chatItemSelected() {
			return this.$store.getters.chatItemSelected;
		},
	},
};
</script>

<style lang="scss">
@import '@/assets/scss/style.scss';

.layout-left {
	top: 0;
	width: var(--sidebar-width);
	box-sizing: border-box;
	padding: 20px;
	background-color: var(--second);
	display: flex;
	flex-direction: column;
	box-shadow: inset -2px 0 2px 0 rgba(0, 0, 0, 0.05);
	position: relative;
	transition: width 0.05s ease;

	.header {
		position: relative;
		padding-top: 20px;
		padding-bottom: 20px;

		.title {
			font-size: 20px;
			font-weight: 700;
		}

		.sub-title {
			font-size: 12px;
			font-weight: 400;
			animation: home_slide-in__h1Bn_ 0.3s ease;
		}
	}

	.header-bar {
		display: flex;

		button {
			flex-grow: 1;
		}
	}

	button {
		background-color: var(--white);
		border-radius: 10px;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 10px;
		cursor: pointer;
		transition: all 0.3s ease;
		overflow: hidden;
		-webkit-user-select: none;
		-moz-user-select: none;
		user-select: none;
		outline: none;
		border: none;
		color: var(--black);
		font-size: 12px;
	}

	.chat-vlist {
		flex: 1 1;
		overflow: auto;
		overflow-x: hidden;
		margin-top: 20px;

		.item {
			padding: 10px 14px;
			background-color: var(--white);
			border-radius: 10px;
			margin-bottom: 10px;
			box-shadow: var(--card-shadow);
			transition: background-color 0.3s ease;
			cursor: pointer;
			-webkit-user-select: none;
			-moz-user-select: none;
			user-select: none;
			border: 2px solid transparent;
			position: relative;
			content-visibility: auto;

			.btn-delete {
				position: absolute;
				top: 0;
				right: 4px;
				display: none;
			}

			&:hover .btn-delete {
				display: block;
			}
		}

		.item.active {
			border: 2px solid var(--primary);
		}

		.item > .title {
			font-size: 14px;
			font-weight: bolder;
			display: block;
			width: calc(100% - 15px);
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.item > .info {
			display: flex;
			justify-content: space-between;
			color: #a6a6a6;
			font-size: 12px;
			margin-top: 8px;
		}
	}

	.tail {
		display: flex;
		justify-content: space-between;
		padding-top: 20px;
	}
}
</style>
