<template>
  <div class="agent-chat">
    <!-- 智能体信息栏 -->
    <div class="agent-info-bar" v-if="selectedAgent">
      <div class="agent-avatar">
        <img :src="selectedAgent.avatar || defaultAvatar" :alt="selectedAgent.name" />
      </div>
      <div class="agent-details">
        <h3 class="agent-name">{{ selectedAgent.name }}</h3>
        <p class="agent-description">{{ selectedAgent.description }}</p>
      </div>
      <div class="agent-actions">
        <el-button size="small" @click="clearChat">清空对话</el-button>
        <el-button size="small" type="primary" @click="showAgentInfo">智能体详情</el-button>
      </div>
    </div>
    
    <!-- 未选择智能体的提示 -->
    <div v-if="!selectedAgent" class="no-agent-selected">
      <div class="empty-state">
        <i class="el-icon-chat-dot-round"></i>
        <h3>请选择一个智能体开始对话</h3>
        <p>从智能体广场选择一个智能体，开始您的AI对话之旅</p>
        <el-button type="primary" @click="goToMarketplace">前往智能体广场</el-button>
      </div>
    </div>
    
    <!-- 对话区域 -->
    <div v-if="selectedAgent" class="chat-area">
      <!-- 对话历史 -->
      <div class="chat-history" ref="chatHistory">
        <div v-for="(message, index) in chatMessages" :key="index" class="message-item">
          <div class="message" :class="message.role">
            <div v-if="message.role === 'assistant'" class="avatar">
              <img :src="selectedAgent.avatar || defaultAvatar" :alt="selectedAgent.name" />
            </div>
            <div class="content">
              <div class="message-content">
                <div v-if="message.starting" class="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
                <div v-else v-html="formatMessage(message.content)"></div>
              </div>
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>
            <div v-if="message.role === 'user'" class="avatar">
              <i class="el-icon-user-solid"></i>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 输入区域 -->
      <div class="chat-input">
        <div class="input-container">
          <el-input
            v-model="inputMessage"
            type="textarea"
            :rows="3"
            placeholder="请输入您的问题..."
            @keydown.ctrl.enter="sendMessage"
            :disabled="isLoading"
          />
          <div class="input-actions">
            <span class="shortcut-tip">Ctrl + Enter 发送</span>
            <el-button 
              type="primary" 
              @click="sendMessage"
              :loading="isLoading"
              :disabled="!inputMessage.trim()"
            >
              发送
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { saveMsgApi } from '@/apis/tools'
import config from '@/apis/config'

export default {
  name: 'agent-chat',
  
  props: {
    selectedAgent: {
      type: Object,
      default: null
    }
  },
  
  data() {
    return {
      chatMessages: [],
      inputMessage: '',
      isLoading: false,
      conversationId: '',
      defaultAvatar: 'https://ai-v2.deepcity.cn/default/head.png'
    }
  },
  
  watch: {
    selectedAgent: {
      handler(newAgent, oldAgent) {
        if (newAgent && (!oldAgent || newAgent.id !== oldAgent.id)) {
          this.initChat()
        }
      },
      immediate: true
    }
  },
  
  methods: {
    initChat() {
      this.chatMessages = []
      this.conversationId = ''
      if (this.selectedAgent) {
        // 添加欢迎消息
        this.chatMessages.push({
          role: 'assistant',
          content: `您好！我是${this.selectedAgent.name}，${this.selectedAgent.description}。有什么可以帮助您的吗？`,
          timestamp: new Date()
        })
        this.scrollToBottom()
      }
    },
    
    async sendMessage() {
      if (!this.inputMessage.trim() || this.isLoading) return
      
      const userMessage = this.inputMessage.trim()
      this.inputMessage = ''
      
      // 添加用户消息
      this.chatMessages.push({
        role: 'user',
        content: userMessage,
        timestamp: new Date()
      })
      
      // 添加AI回复占位符
      this.chatMessages.push({
        role: 'assistant',
        content: '',
        starting: true,
        timestamp: new Date()
      })
      
      this.scrollToBottom()
      this.isLoading = true
      
      try {
        // 保存用户消息
        const msgReq = {
          content: userMessage,
          lastMsgId: '',
          aiModelGuid: this.selectedAgent.id
        }
        
        const res = await saveMsgApi(msgReq)
        
        if (res.code === 0) {
          await this.waitForAIResponse(res.data.msgId)
        } else {
          this.handleError('发送消息失败')
        }
      } catch (error) {
        console.error('发送消息错误:', error)
        this.handleError('发送消息失败，请重试')
      }
    },
    
    async waitForAIResponse(msgId) {
      const lastMessage = this.chatMessages[this.chatMessages.length - 1]
      
      try {
        const link = `${config.baseUrl}square/api.chat/sendAgentOpen?msgId=${msgId}&aiAgentGuid=${this.selectedAgent.id}&userGuid=${this.$store.state.userGuid}&conversation_id=${this.conversationId}&merchantGuid=${this.$store.state.merchantGuid}`
        
        const source = new EventSource(link)
        
        source.onmessage = (event) => {
          const data = event.data
          
          if (data === '[DONE]') {
            lastMessage.starting = false
            lastMessage.finished = true
            this.conversationId = event.lastEventId
            this.isLoading = false
            source.close()
          } else if (data) {
            lastMessage.starting = false
            lastMessage.content += data.replace(/\\n/g, '\n')
            this.scrollToBottom()
          }
        }
        
        source.onerror = () => {
          this.handleError('AI回复失败')
          source.close()
        }
        
        source.onopen = () => {
          console.log('AI连接已建立')
        }
        
      } catch (error) {
        this.handleError('AI回复失败')
      }
    },
    
    handleError(message) {
      const lastMessage = this.chatMessages[this.chatMessages.length - 1]
      if (lastMessage && lastMessage.role === 'assistant') {
        lastMessage.starting = false
        lastMessage.content = message
        lastMessage.error = true
      }
      this.isLoading = false
      this.$message.error(message)
    },
    
    clearChat() {
      this.$confirm('确定要清空当前对话吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.initChat()
        this.$message.success('对话已清空')
      })
    },
    
    showAgentInfo() {
      this.$alert(
        `<div>
          <p><strong>名称：</strong>${this.selectedAgent.name}</p>
          <p><strong>描述：</strong>${this.selectedAgent.description}</p>
          <p><strong>评分：</strong>${this.selectedAgent.rating}</p>
          <p><strong>使用次数：</strong>${this.selectedAgent.usageCount}</p>
          <p><strong>标签：</strong>${this.selectedAgent.tags.join(', ')}</p>
        </div>`,
        '智能体详情',
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定'
        }
      )
    },
    
    goToMarketplace() {
      this.$emit('go-to-marketplace')
    },
    
    formatMessage(content) {
      if (!content) return ''
      return content.replace(/\n/g, '<br>')
    },
    
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    
    scrollToBottom() {
      this.$nextTick(() => {
        const chatHistory = this.$refs.chatHistory
        if (chatHistory) {
          chatHistory.scrollTop = chatHistory.scrollHeight
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.agent-chat {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  
  .agent-info-bar {
    background: white;
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
    gap: 16px;
    
    .agent-avatar {
      img {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        object-fit: cover;
      }
    }
    
    .agent-details {
      flex: 1;
      
      .agent-name {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
      
      .agent-description {
        margin: 0;
        font-size: 14px;
        color: #666;
        line-height: 1.4;
      }
    }
    
    .agent-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .no-agent-selected {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    
    .empty-state {
      text-align: center;
      
      i {
        font-size: 64px;
        color: #ddd;
        margin-bottom: 16px;
      }
      
      h3 {
        margin: 0 0 8px 0;
        color: #333;
      }
      
      p {
        margin: 0 0 24px 0;
        color: #666;
      }
    }
  }
  
  .chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    
    .chat-history {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      
      .message-item {
        margin-bottom: 20px;
        
        .message {
          display: flex;
          gap: 12px;
          
          &.user {
            flex-direction: row-reverse;
            
            .content {
              text-align: right;
              
              .message-content {
                background: #7848f1;
                color: white;
              }
            }
          }
          
          .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f0f0f0;
            flex-shrink: 0;
            
            img {
              width: 100%;
              height: 100%;
              border-radius: 50%;
              object-fit: cover;
            }
            
            i {
              font-size: 20px;
              color: #666;
            }
          }
          
          .content {
            flex: 1;
            max-width: 70%;
            
            .message-content {
              background: white;
              padding: 12px 16px;
              border-radius: 12px;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
              word-wrap: break-word;
              line-height: 1.5;
              
              .typing-indicator {
                display: flex;
                gap: 4px;
                
                span {
                  width: 8px;
                  height: 8px;
                  border-radius: 50%;
                  background: #999;
                  animation: typing 1.4s infinite ease-in-out;
                  
                  &:nth-child(1) { animation-delay: -0.32s; }
                  &:nth-child(2) { animation-delay: -0.16s; }
                }
              }
            }
            
            .message-time {
              font-size: 12px;
              color: #999;
              margin-top: 4px;
            }
          }
        }
      }
    }
    
    .chat-input {
      background: white;
      border-top: 1px solid #e8e8e8;
      padding: 20px;
      
      .input-container {
        .el-textarea {
          margin-bottom: 12px;
        }
        
        .input-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .shortcut-tip {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
</style>
