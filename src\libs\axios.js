import axios from 'axios'

import { randomStr, autographFun } from './axios-lib'
import { merchantGuid } from '@/config';
const axiosIns = axios.create({
  // baseURL: process.env.VUE_APP_REST,
  baseURL: 'https://ai-api.deepcity.cn',
  timeout: 10000
})

axiosIns.interceptors.request((config) => {
  // 配置参数和全局配置相同，此优先级最高，会覆盖在其他地方的相同配置参数
  // 追加请求头，推荐
  // config.header['content-type'] = 'application/json';
  // config.header['tourist-no'] = store.state.randomStr32;//随机32位数据
  // config.header['version'] = store.state.version;//版本号
  config.urlSuffix = {
    app_type: 'wechat', //应用类型：wechat 微信
    app_guid: merchantGuid, //应用唯一标识
    expires: parseInt((new Date().getTime() / 1000).toFixed(0)), //当前时间戳
    token: store.state.userToken || 'notoken',
    noncestr: randomStr(true, true, true, 32),
  };

  if (!config.header.ifTouristLogin) {
    config.urlSuffix.signature = encodeURIComponent(autographFun(config)); //签名
  }

  return config; // 返回修改后的配置，如未修改也需添加这行
})

axiosIns.interceptors.response.use((response) => {

  const body = response.data
  if (body.status != 1) {
    return body;
  } else {
    return response.data;
  }

}, (err) => {

  // console.log("ERROR : ", err)

  if (err && err.response) {

    err.message = 'Network Error'

    // switch (err.response.status) {
    //     case 400: err.message = '请求错误(400)' ; break;
    //     case 401: err.message = '未授权，请重新登录(401)'; break;
    //     case 403: err.message = '拒绝访问(403)'; break;
    //     case 404: err.message = '请求出错(404)'; break;
    //     case 408: err.message = '请求超时(408)'; break;
    //     case 500: err.message = '服务器错误(500)'; break;
    //     case 501: err.message = '服务未实现(501)'; break;
    //     case 502: err.message = '网络错误(502)'; break;
    //     case 503: err.message = '服务不可用(503)'; break;
    //     case 504: err.message = '网络超时(504)'; break;
    //     case 505: err.message = 'HTTP版本不受支持(505)'; break;
    //     default: err.message = `连接出错(${err.response.status})！`;
    // }

  } else {

    err.message = 'Network Error'
  }

  // failToast('Server Error',err.message)

  return Promise.reject(err);
})

// Vue.prototype.$http = axiosIns

// export default axiosIns
